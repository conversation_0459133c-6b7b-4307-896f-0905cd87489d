import * as sinon from 'sinon';
import * as chai from 'chai';
import { initiativeOneSimpleId } from '../../../tests/fixtures/initiativeFixtures';
import { createMongooseModel } from '../../setup';
import InitiativeUniversalTracker, { type InitiativeUtrMin } from '../../../server/models/initiativeUniversalTracker';
import {
  InitiativeUniversalTrackerService,
  getInitiativeUniversalTrackerService,
} from '../../../server/service/initiative/InitiativeUniversalTrackerService';
import { ObjectId } from 'bson';
import { ColumnType, UtrValueType, VariationDataSource, VariationType, ValueAggregation } from '../../../server/models/public/universalTrackerType';
import UniversalTracker from '../../../server/models/universalTracker';
import { NumberScale } from '../../../server/service/units/unitTypes';
import { type RootInitiativeData } from '../../../server/repository/InitiativeRepository';
import { getRootInitiativeService } from '../../../server/service/organization/RootInitiativeService';
import { type PipelineStage } from 'mongoose';

const baseColumn = { code: 'test', type: ColumnType.Number, name: 'test' };

const variationParams = {
  type: VariationType.Percentage,
  min: 0,
  max: 0,
  dataSource: VariationDataSource.LastMonth,
  confirmationRequired: false,
};

describe('InitiativeUniversalTrackerService', () => {
  const sandbox = sinon.createSandbox();
  const rootInitiativeService = getRootInitiativeService();
  const service = new InitiativeUniversalTrackerService(rootInitiativeService, InitiativeUniversalTracker);
  const rootOrgId = new ObjectId();

  describe('getUtrOverridesMap fn', () => {
    const utrOneConfig: InitiativeUtrMin = {
      _id: new ObjectId(),
      universalTrackerId: new ObjectId(),
    };
    const utrTwoConfig: InitiativeUtrMin = {
      _id: new ObjectId(),
      universalTrackerId: new ObjectId(),
      valueValidation: {
        decimal: 2,
      },
    };
    const utrThreeConfig: InitiativeUtrMin = {
      _id: new ObjectId(),
      universalTrackerId: new ObjectId(),
      valueValidation: {
        table: { columns: [] },
      },
    };
    const utrFourConfig: InitiativeUtrMin = {
      _id: new ObjectId(),
      universalTrackerId: new ObjectId(),
      valueValidation: {
        decimal: 4,
        table: { columns: [] },
      },
    };
    const initiativeUtrsConfig = [utrOneConfig, utrTwoConfig, utrThreeConfig, utrFourConfig];

    beforeEach(() => {
      sandbox.stub(rootInitiativeService, 'getOrganizationById').returns(createMongooseModel({ _id: rootOrgId } as RootInitiativeData));
    });

    afterEach(() => {
      sandbox.restore();
    });

    it('When not find any initiative utr config, then return empty Map', async () => {
      sandbox.stub(InitiativeUniversalTracker, 'find').returns(createMongooseModel([]));
      const map = await service.getUtrOverridesMap({ initiativeId: initiativeOneSimpleId, utrIds: [] });
      chai.expect(map.size).to.be.eq(0);
    });

    it('When find an array of initiative utr configs, then return a correct Map', async () => {
      sandbox.stub(InitiativeUniversalTracker, 'find').returns(createMongooseModel(initiativeUtrsConfig));
      const map = await service.getUtrOverridesMap({
        initiativeId: initiativeOneSimpleId,
        utrIds: initiativeUtrsConfig.map(({ universalTrackerId }) => universalTrackerId),
      });
      chai.expect(map.size).to.be.eq(4);
      [utrTwoConfig, utrThreeConfig, utrFourConfig].forEach((item) => {
        chai.expect(map.get(item.universalTrackerId.toString())?.valueValidation).to.be.deep.eq(item.valueValidation);
      });
    });
  });

  describe('setOverriddenValueValidation fn', () => {
    const service = new InitiativeUniversalTrackerService(getRootInitiativeService(), InitiativeUniversalTracker);
    let bulkWriteStub: sinon.SinonStub;

    beforeEach(() => {
      bulkWriteStub = sandbox.stub(InitiativeUniversalTracker, 'bulkWrite');
      sandbox.stub(service, 'getRootInitiativeUniversalTrackers').resolves([]);
    });

    afterEach(() => {
      sandbox.restore();
    });

    it('should handle unknown value types gracefully', async () => {
      const mockUtrs = [{ _id: new ObjectId(), valueType: UtrValueType.Text }];

      const params = {
        decimal: { __DATA__: 2 },
        unitConfig: { __DATA__: {} },
        initiativeId: new ObjectId(),
        utrIds: mockUtrs.map((utr) => utr._id.toString()),
      };
      sandbox.stub(UniversalTracker, 'find').returns(createMongooseModel(mockUtrs));

      await service.setOverriddenValueValidation(params);

      chai.expect(bulkWriteStub.calledOnceWith([])).to.be.true;
    });

    it('should handle empty updates', async () => {
      const params = {
        decimal: { __DATA__: 2 },
        unitConfig: { __DATA__: {} },
        initiativeId: new ObjectId(),
        utrIds: [],
      };
      sandbox.stub(UniversalTracker, 'find').returns(createMongooseModel([]));

      await service.setOverriddenValueValidation(params);
      chai.expect(bulkWriteStub.calledOnceWith([])).to.be.true;
    });

    it('should handle empty updates if table question has no columns', async () => {
      const universalTrackerId = new ObjectId();
      const initiativeId = new ObjectId();
      const mockUtrs = [
        {
          _id: universalTrackerId,
          valueType: UtrValueType.Table,
          valueValidation: { table: { columns: [] } },
        },
      ];

      const params = {
        initiativeId,
        decimal: { __DATA__: 2 },
        unitConfig: { __DATA__: {} },
        utrIds: [universalTrackerId.toString()],
      };
      sandbox.stub(UniversalTracker, 'find').returns(createMongooseModel(mockUtrs));

      await service.setOverriddenValueValidation(params);

      chai.expect(bulkWriteStub.calledOnceWith([])).to.be.true;
    });

    it('should update overrides for Number question correctly', async () => {
      const universalTrackerId = new ObjectId();
      const initiativeId = new ObjectId();
      const mockUtrs = [
        {
          _id: universalTrackerId,
          valueType: UtrValueType.Number,
          unit: 'USD',
          unitType: 'currency',
          numberScale: 'millions',
        },
      ];

      const params = {
        initiativeId,
        decimal: { __DATA__: null }, // reset decimal by sending null
        unitConfig: { __DATA__: { volume: 'Ml', numberScale: NumberScale.Thousands } },
        variation: { __DATA__: [variationParams] },
        utrIds: mockUtrs.map(({ _id }) => _id.toString()),
      };

      sandbox.stub(UniversalTracker, 'find').returns(createMongooseModel(mockUtrs));

      await service.setOverriddenValueValidation(params);

      const operations = [
        {
          updateOne: {
            filter: { universalTrackerId: mockUtrs[0]._id, initiativeId },
            update: {
              $set: {
                numberScaleInput: 'thousands',
                numberScaleLocked: false,
                'valueValidation.variations': [variationParams],
              },
              $unset: {
                'valueValidation.decimal': '',
              },
            },
            upsert: true,
          },
        },
      ];

      chai.expect(bulkWriteStub.calledOnceWith(operations)).to.be.true;
    });

    it('should update numberScaleInput for Numeric question correctly', async () => {
      const universalTrackerId = new ObjectId();
      const initiativeId = new ObjectId();
      const mockUtrs = [
        {
          _id: universalTrackerId,
          valueType: UtrValueType.Percentage,
        },
      ];

      const params = {
        initiativeId,
        decimal: { __DATA__: null },
        unitConfig: { __DATA__: { numberScale: NumberScale.Thousands } },
        utrIds: mockUtrs.map(({ _id }) => _id.toString()),
      };

      sandbox.stub(UniversalTracker, 'find').returns(createMongooseModel(mockUtrs));

      await service.setOverriddenValueValidation(params);

      const operations = mockUtrs.map(({ _id }) => ({
        updateOne: {
          filter: { universalTrackerId: _id, initiativeId },
          update: {
            $set: {
              numberScaleInput: 'thousands',
              numberScaleLocked: false, 
            },
            $unset: {
              'valueValidation.decimal': '',
            },
          },
          upsert: true,
        },
      }));

      chai.expect(bulkWriteStub.calledOnceWith(operations)).to.be.true;
    });

    it('should update overrides for Table question correctly', async () => {
      const universalTrackerId = new ObjectId();
      const initiativeId = new ObjectId();

      // column has non-numeric type or has calculation will not be allowed to set overrides
      const textColumn = { ...baseColumn, type: ColumnType.Text, code: 'colText' };
      const calculationColumn = {
        code: 'colCalculation',
        name: 'Col Calculation',
        type: ColumnType.Number,
        calculation: { formula: '{colNum}/{colCurrency}*100' },
      };
      const columns = [
        baseColumn,
        { ...baseColumn, code: 'colNum', unit: 'm3', unitType: 'volume' },
        { ...baseColumn, code: 'colCurrency', unit: 'USD', unitType: 'currency', numberScale: 'millions' },
        textColumn,
        calculationColumn,
      ];
      const mockUtrs = [
        {
          _id: universalTrackerId,
          valueType: UtrValueType.Table,
          valueValidation: { table: { columns } },
        },
      ];

      const params = {
        decimal: { colNum: 1, colCurrency: 2 },
        unitConfig: {
          test: { numberScale: NumberScale.Thousands },
          colNum: { volume: 'Ml', numberScale: NumberScale.Thousands },
          colCurrency: { numberScale: NumberScale.Thousands },
        },
        variation: {
          colNum: [variationParams],
          colCurrency: [{...variationParams, min: -100, max: 100 }],
        },
        initiativeId,
        utrIds: [universalTrackerId.toString()],
      };

      sandbox.stub(UniversalTracker, 'find').returns(createMongooseModel(mockUtrs));

      await service.setOverriddenValueValidation(params);

      const updatedColumns = [
        {
          ...baseColumn,
          numberScaleInput: 'thousands',
          numberScaleLocked: undefined,
          validation: { decimal: undefined, variations: undefined },
        },
        {
          ...baseColumn,
          code: 'colNum',
          validation: { decimal: 1, variations: [variationParams] },
          unitInput: 'Ml',
          numberScaleInput: 'thousands', // numberScale is always set if available
          unitLocked: undefined,
          numberScaleLocked: undefined,
        },
        {
          ...baseColumn,
          code: 'colCurrency',
          validation: { decimal: 2, variations: [{ ...variationParams, min: -100, max: 100 }] },
          numberScaleInput: 'thousands',
          numberScaleLocked: undefined,
        },
      ];

      const operations = [
        {
          updateOne: {
            filter: { universalTrackerId, initiativeId },
            update: { valueValidation: { table: { columns: updatedColumns } } },
            upsert: true,
          },
        },
      ];

      chai.expect(bulkWriteStub.calledOnceWith(operations)).to.be.true;
    });

    describe('overrides multiple simple Numeric & Table questions', () => {
      const initiativeId = new ObjectId();

      const mockUtrs = [
        {
          _id: new ObjectId(),
          valueType: UtrValueType.Number,
        },
        {
          _id: new ObjectId(),
          valueType: UtrValueType.Percentage,
        },
        {
          _id: new ObjectId(),
          valueType: UtrValueType.NumericValueList,
          unit: 'm3',
          unitType: 'volume',
        },
        {
          _id: new ObjectId(),
          valueType: UtrValueType.Table,
          valueValidation: { table: { columns: [{ ...baseColumn, unit: 'm3', unitType: 'volume' }] } },
        },
      ];

      const params = { initiativeId, utrIds: mockUtrs.map(({ _id }) => _id.toString()) };

      const withDecimalParams = { ...params, decimal: { __DATA__: 2 } };
      const expectedDecimalUpdates = [
        { update: { $set: { 'valueValidation.decimal': 2 } }, upsert: true },
        { update: { $set: { 'valueValidation.decimal': 2 } }, upsert: true },
        { update: { $set: { 'valueValidation.decimal': 2 } }, upsert: true },
        {
          update: { valueValidation: { table: { columns: [{ ...baseColumn, validation: { decimal: 2 } }] } } },
          upsert: true,
        },
      ];

      const withNumberScaleParams = { ...params, unitConfig: { __DATA__: { numberScale: 'thousands' } } };
      const expectedNumberScaleUpdates = [
        { update: { $set: { numberScaleInput: 'thousands', numberScaleLocked: false } }, upsert: true },
        { update: { $set: { numberScaleInput: 'thousands', numberScaleLocked: false } }, upsert: true },
        { update: { $set: { numberScaleInput: 'thousands', numberScaleLocked: false } }, upsert: true },
        {
          update: {
            valueValidation: {
              table: { columns: [{ ...baseColumn, numberScaleInput: 'thousands', numberScaleLocked: undefined }] },
            },
          },
          upsert: true,
        },
      ];

      const withUnitParams = { ...params, unitConfig: { __DATA__: { volume: 'Ml' } } };
      const expectedUnitUpdates = [
        undefined,
        undefined,
        { update: { $set: { unitInput: 'Ml', unitLocked: false } }, upsert: true },
        {
          update: {
            valueValidation: { table: { columns: [{ ...baseColumn, unitInput: 'Ml', unitLocked: undefined }] } },
          },
          upsert: true,
        },
      ];

      const withResetDecimalParams = { ...params, decimal: { __DATA__: null } };
      const expectedResetDecimalUpdates = [
        { update: { $unset: { 'valueValidation.decimal': '' } }, upsert: false },
        { update: { $unset: { 'valueValidation.decimal': '' } }, upsert: false },
        { update: { $unset: { 'valueValidation.decimal': '' } }, upsert: false },
        {
          update: { valueValidation: { table: { columns: [{ ...baseColumn, validation: { decimal: undefined } }] } } },
          upsert: false,
        },
      ];

      const withResetNumberScaleParams = { ...params, unitConfig: { __DATA__: { numberScale: '' } } };
      const expectedResetNumberScaleUpdates = [
        { update: { $unset: { numberScaleInput: '', numberScaleLocked: '' } }, upsert: false },
        { update: { $unset: { numberScaleInput: '', numberScaleLocked: '' } }, upsert: false },
        { update: { $unset: { numberScaleInput: '', numberScaleLocked: '' } }, upsert: false },
        {
          update: {
            valueValidation: {
              table: { columns: [{ ...baseColumn, numberScaleInput: '', numberScaleLocked: undefined }] },
            },
          },
          upsert: false,
        },
      ];

      const withVariationParams = { ...params, variation: { __DATA__: [variationParams] } };
      const expectedVariationUpdates = [
        { update: { $set: { 'valueValidation.variations': [variationParams] } }, upsert: true },
        { update: { $set: { 'valueValidation.variations': [variationParams] } }, upsert: true },
        { update: { $set: { 'valueValidation.variations': [variationParams] } }, upsert: true },
        {
          update: {
            valueValidation: { table: { columns: [{ ...baseColumn, validation: { variations: [variationParams] } }] } },
          },
          upsert: true,
        },
      ];

      const withResetVariationParams = { ...params, variation: { __DATA__: null } };
      const expectedResetVariationUpdates = [
        { update: { $unset: { 'valueValidation.variations': '' } }, upsert: false },
        { update: { $unset: { 'valueValidation.variations': '' } }, upsert: false },
        { update: { $unset: { 'valueValidation.variations': '' } }, upsert: false },
        {
          update: { valueValidation: { table: { columns: [{ ...baseColumn, validation: { variations: undefined } }] } } },
          upsert: false,
        },
      ];

      const getOperations = (expectedUpdates: (Record<string, any> | undefined)[]) => {
        return [mockUtrs[0]._id, mockUtrs[1]._id, mockUtrs[2]._id, mockUtrs[3]._id]
          .map((universalTrackerId, index) => {
            if (expectedUpdates[index]) {
              return {
                updateOne: {
                  filter: { universalTrackerId, initiativeId },
                  update: expectedUpdates[index].update,
                  upsert: expectedUpdates[index].upsert,
                },
              };
            }
          })
          .filter((item) => item !== undefined);
      };

      const dataProvider = [
        {
          title: 'update decimal only',
          params: withDecimalParams,
          operations: getOperations(expectedDecimalUpdates),
        },
        {
          title: 'update numberScale only',
          params: withNumberScaleParams,
          operations: getOperations(expectedNumberScaleUpdates),
        },
        {
          title: 'update unit only',
          params: withUnitParams,
          operations: getOperations(expectedUnitUpdates),
        },
        {
          title: 'reset decimal only',
          params: withResetDecimalParams,
          operations: getOperations(expectedResetDecimalUpdates),
        },
        {
          title: 'reset number scale only',
          params: withResetNumberScaleParams,
          operations: getOperations(expectedResetNumberScaleUpdates),
        },
        {
          title: 'update variation only',
          params: withVariationParams,
          operations: getOperations(expectedVariationUpdates),
        },
        {
          title: 'reset variation only',
          params: withResetVariationParams,
          operations: getOperations(expectedResetVariationUpdates),
        },
      ];
      dataProvider.forEach(({ title, params, operations }) => {
        it(title, async () => {
          sandbox.stub(UniversalTracker, 'find').returns(createMongooseModel(mockUtrs));
          await service.setOverriddenValueValidation(params);
          chai.expect(bulkWriteStub.calledOnceWith(operations)).to.be.true;
        });
      });
    });
  });

  describe('getRootInitiativeUniversalTrackers', () => {
    afterEach(() => {
      sandbox.restore();
    });

    it('should return an empty array if no initiative is found', async () => {
      await chai
        .expect(service.getRootInitiativeUniversalTrackers(new ObjectId()))
        .to.be.eventually.rejectedWith(/No document found for query/);
    });

    it('should return the universal trackers for the root organization', async () => {
      const initiativeId = new ObjectId();
      const rootOrgId = new ObjectId();

      const rootInitiativeServiceStub = sandbox
        .stub(rootInitiativeService, 'getOrganizationById')
        .resolves({ _id: rootOrgId } as RootInitiativeData);
      const universalTrackers = [
        { initiativeId: rootOrgId._id, universalTrackerId: new ObjectId() },
        { initiativeId: rootOrgId._id, universalTrackerId: new ObjectId() },
      ];

      sandbox.stub(InitiativeUniversalTracker, 'find').returns(createMongooseModel(universalTrackers));
      const result = await service.getRootInitiativeUniversalTrackers(initiativeId);
      chai.expect(rootInitiativeServiceStub.calledOnce).to.be.true;
      chai.expect(result).to.deep.eq(universalTrackers);
    });
  });

  describe('getPipelineStagesOnOverride', () => {    
    beforeEach(() => {
      sandbox
        .stub(rootInitiativeService, 'getOrganizationById')
        .resolves({ _id: rootOrgId } as RootInitiativeData);
    });

    afterEach(() => {
      sandbox.restore();
    });
    it('should return correct data', async () => {
      const { initiativeUtrLookupAndUnwind, initiativeUtrOverrides } =
        await service.getPipelineStagesOnOverride(new ObjectId());
        const lookupObj = initiativeUtrLookupAndUnwind[0] as PipelineStage.Lookup;
      chai.expect(lookupObj.$lookup.pipeline).to.deep.equal([
        {
          $match: {
            $expr: {
              $and: [{ $eq: ['$universalTrackerId', '$$utrId'] }, { $eq: ['$initiativeId', rootOrgId] }],
            },
          },
        },
      ]);
      chai.expect(initiativeUtrOverrides).not.to.be.undefined;
    });
  });

  describe('setAggregationConfig', () => {
    let mockInitiativeUniversalTrackerModel: sinon.SinonStubbedInstance<typeof InitiativeUniversalTracker>;
    let service: InitiativeUniversalTrackerService;

    beforeEach(() => {
      mockInitiativeUniversalTrackerModel = {
        updateOne: sandbox.stub().resolves({ acknowledged: true, matchedCount: 1, modifiedCount: 1, upsertedCount: 0 }),
      } as any;

      service = new InitiativeUniversalTrackerService(rootInitiativeService, mockInitiativeUniversalTrackerModel as any);
    });

    afterEach(() => {
      sandbox.restore();
    });

    const initiativeId = new ObjectId();
    const utrId = new ObjectId();

    it('should throw error if UTR is not found', async () => {
      const aggregationConfig = {
        modes: {
          children: { valueAggregation: ValueAggregation.ValueSumAggregator },
          combined: { valueAggregation: ValueAggregation.ValueAverageAggregator },
        },
      };
      
      sandbox.stub(UniversalTracker, 'findById').returns(createMongooseModel(null));
      
      await chai.expect(service.setAggregationConfig({
        initiativeId,
        utrId,
        aggregationConfig,
      })).to.be.rejectedWith(/Document not found/);
    });

    it('should successfully set aggregation config for Number UTR', async () => {
      const aggregationConfig = {
        modes: {
          children: { valueAggregation: ValueAggregation.ValueSumAggregator },
          combined: { valueAggregation: ValueAggregation.ValueAverageAggregator },
        },
      };

      const mockUtr = { _id: utrId, valueType: UtrValueType.Number };

      sandbox.stub(UniversalTracker, 'findById').returns(createMongooseModel(mockUtr));

      await service.setAggregationConfig({
        initiativeId,
        utrId,
        aggregationConfig,
      });

      chai.expect(mockInitiativeUniversalTrackerModel.updateOne.calledOnce).to.be.true;
    });

    it('should throw error for incompatible aggregation settings for Text UTRs', async () => {
      const aggregationConfig = {
        modes: {
          children: { valueAggregation: ValueAggregation.ValueSumAggregator }, // Incompatible with Text
          combined: { valueAggregation: ValueAggregation.ValueConcatenateAggregator }, // Compatible with Text
        },
      };

      const mockUtr = { _id: utrId, valueType: UtrValueType.Text };

      sandbox.stub(UniversalTracker, 'findById').returns(createMongooseModel(mockUtr));

      await chai.expect(service.setAggregationConfig({
        initiativeId,
        utrId,
        aggregationConfig,
      })).to.be.rejectedWith(/Incompatible aggregation.*valueSumAggregator.*children mode.*text/);
    });

    it('should handle empty modes object', async () => {
      const aggregationConfig = {
        modes: {
          // Empty modes object - should be handled gracefully
        },
      };

      const mockUtr = { _id: utrId, valueType: UtrValueType.Number };

      sandbox.stub(UniversalTracker, 'findById').returns(createMongooseModel(mockUtr));

      await service.setAggregationConfig({
        initiativeId,
        utrId,
        aggregationConfig,
      });

      chai.expect(mockInitiativeUniversalTrackerModel.updateOne.calledOnce).to.be.true;
    });

    it('should handle Table UTRs with compatible aggregation settings', async () => {
      const aggregationConfig = {
        modes: {
          children: { valueAggregation: ValueAggregation.TableColumnAggregator },
          combined: { valueAggregation: ValueAggregation.TableConcatenationAggregator },
        },
      };

      const mockUtr = { _id: utrId, valueType: UtrValueType.Table };

      sandbox.stub(UniversalTracker, 'findById').returns(createMongooseModel(mockUtr));

      await service.setAggregationConfig({
        initiativeId,
        utrId,
        aggregationConfig,
      });

      chai.expect(mockInitiativeUniversalTrackerModel.updateOne.calledOnce).to.be.true;
    });

    it('should handle only children mode provided', async () => {
      const aggregationConfig = {
        modes: {
          children: { valueAggregation: ValueAggregation.ValueSumAggregator },
          // Only children mode provided, combined is optional
        },
      };

      const mockUtr = { _id: utrId, valueType: UtrValueType.Number };

      sandbox.stub(UniversalTracker, 'findById').returns(createMongooseModel(mockUtr));

      await service.setAggregationConfig({
        initiativeId,
        utrId,
        aggregationConfig,
      });

      chai.expect(mockInitiativeUniversalTrackerModel.updateOne.calledOnce).to.be.true;
    });

    it('should handle only combined mode provided', async () => {
      const aggregationConfig = {
        modes: {
          combined: { valueAggregation: ValueAggregation.ValueAverageAggregator },
          // Only combined mode provided, children is optional
        },
      };

      const mockUtr = { _id: utrId, valueType: UtrValueType.Number };

      sandbox.stub(UniversalTracker, 'findById').returns(createMongooseModel(mockUtr));

      await service.setAggregationConfig({
        initiativeId,
        utrId,
        aggregationConfig,
      });

      chai.expect(mockInitiativeUniversalTrackerModel.updateOne.calledOnce).to.be.true;
    });
  });

  describe('getInitiativeUniversalTrackerService', () => {
    it('should return a singleton instance', () => {
      const instance1 = getInitiativeUniversalTrackerService();
      const instance2 = getInitiativeUniversalTrackerService();
      chai.expect(instance1).to.equal(instance2);
    });
  });
});
