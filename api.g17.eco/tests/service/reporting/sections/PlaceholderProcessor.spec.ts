import { expect } from 'chai';
import sinon from 'sinon';
import { PlaceholderProcessor, getPlaceholderProcessor } from '../../../../server/service/reporting/sections/PlaceholderProcessor';
import type { ExtendedTagMappingItem, XBRLMapping, UtrvData } from '../../../../server/service/reporting/types';
import { ObjectId } from 'bson';
import { XbrlTracker } from '../../../../server/service/reporting/XbrlTracker';
import { createHeadlessEditor } from '@lexical/headless';
import { editorConfig } from '../../../../server/service/reporting/utils';
import { $createTextNode } from 'lexical';
import { universalTrackerOne } from '../../../fixtures/universalTrackerFixtures';
import * as lexicalUtils from '../../../../server/service/reporting/lexical/utils';
import { ActionList } from '../../../../server/types/constants';
import type { IXBRLNode } from '../../../../server/service/reporting/lexical/nodes/IXBRLNode';

describe('PlaceholderProcessor', () => {
  const sandbox = sinon.createSandbox();
  let processor: PlaceholderProcessor;
  let mockTracker: XbrlTracker;

  // Helper function to run code in editor context
  const runInEditor = <T>(fn: () => T): T => {
    const editor = createHeadlessEditor(editorConfig);
    let result: T;
    editor.update(() => {
      result = fn();
    }, { discrete: true });
    return result!;
  };
  
  const mockReportContext = {
    initiativeName: 'Test Company',
    effectiveDate: '2023'
  };

  const mockXBRLMapping: XBRLMapping<ExtendedTagMappingItem> = {
    'test:Revenue': {
      factName: 'test:Revenue',
      utrCode: 'REV001',
      valueListCode: 'VL001',
      utrvId: new ObjectId(),
    }
  };

  const mockUtrvData: UtrvData[] = [
    {
      _id: new ObjectId(),
      value: 100,
      valueData: {},
      status: ActionList.Verified,
      effectiveDate: new Date(),
      universalTracker: universalTrackerOne,
    },
  ];

  const createUtrCodeToUtrvMap = (): Map<string, UtrvData> => {
    return new Map([['REV001', mockUtrvData[0]]]);
  };

  beforeEach(() => {
    processor = new PlaceholderProcessor();
    mockTracker = new XbrlTracker();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('splitTextIntoChunks', () => {
    it('should split text with placeholders correctly', () => {
      const text = 'Hello {{{initiativeName}}} world {{{effectiveDate}}}';
      const result = processor.splitTextIntoChunks(text);
      
      expect(result).to.deep.equal([
        'Hello ',
        '{{{initiativeName}}}',
        ' world ',
        '{{{effectiveDate}}}'
      ]);
    });

    it('should handle text without placeholders', () => {
      const text = 'Hello world';
      const result = processor.splitTextIntoChunks(text);
      
      expect(result).to.deep.equal(['Hello world']);
    });

    it('should handle text with only placeholders', () => {
      const text = '{{{initiativeName}}}{{{effectiveDate}}}';
      const result = processor.splitTextIntoChunks(text);
      
      expect(result).to.deep.equal(['{{{initiativeName}}}', '{{{effectiveDate}}}']);
    });

    it('should handle empty text', () => {
      const text = '';
      const result = processor.splitTextIntoChunks(text);
      
      expect(result).to.deep.equal([]);
    });

    it('should filter out empty chunks', () => {
      const text = '{{{test}}}';
      const result = processor.splitTextIntoChunks(text);
      
      expect(result).to.deep.equal(['{{{test}}}']);
    });
  });

  describe('isPlaceholder', () => {
    it('should identify valid placeholders', () => {
      expect(processor.isPlaceholder('{{{initiativeName}}}')).to.be.true;
      expect(processor.isPlaceholder('{{{test:Revenue}}}')).to.be.true;
      expect(processor.isPlaceholder('{{{effectiveDate}}}')).to.be.true;
    });

    it('should reject invalid placeholder formats', () => {
      expect(processor.isPlaceholder('{{initiativeName}}')).to.be.false;
      expect(processor.isPlaceholder('{initiativeName}')).to.be.false;
      expect(processor.isPlaceholder('initiativeName')).to.be.false;
      expect(processor.isPlaceholder('{{{}}}')).to.be.false;
      expect(processor.isPlaceholder('{{{}}')).to.be.false;
    });
  });

  describe('extractPlaceholderName', () => {
    it('should extract placeholder name correctly', () => {
      expect(processor.extractPlaceholderName('{{{initiativeName}}}')).to.equal('initiativeName');
      expect(processor.extractPlaceholderName('{{{test:Revenue}}}')).to.equal('test:Revenue');
      expect(processor.extractPlaceholderName('{{{effectiveDate}}}')).to.equal('effectiveDate');
    });

    it('should handle malformed placeholders gracefully', () => {
      expect(processor.extractPlaceholderName('{{initiativeName}}')).to.equal('{{initiativeName}}');
      expect(processor.extractPlaceholderName('{initiativeName}')).to.equal('{initiativeName}');
    });
  });

  describe('isCommonPlaceholder', () => {
    it('should identify common placeholders', () => {
      expect(processor.isCommonPlaceholder('initiativeName')).to.be.true;
      expect(processor.isCommonPlaceholder('effectiveDate')).to.be.true;
    });

    it('should reject non-common placeholders', () => {
      expect(processor.isCommonPlaceholder('test:Revenue')).to.be.false;
      expect(processor.isCommonPlaceholder('unknownPlaceholder')).to.be.false;
      expect(processor.isCommonPlaceholder('')).to.be.false;
    });
  });

  describe('getCommonPlaceholderValue', () => {
    it('should return correct values for common placeholders', () => {
      expect(processor.getCommonPlaceholderValue('initiativeName', mockReportContext))
        .to.equal('Test Company');
      expect(processor.getCommonPlaceholderValue('effectiveDate', mockReportContext))
        .to.equal('2023');
    });

    it('should fallback to placeholder name for unknown placeholders', () => {
      expect(processor.getCommonPlaceholderValue('unknownPlaceholder', mockReportContext))
        .to.equal('unknownPlaceholder');
    });
  });

  describe('isXBRLTag', () => {
    it('should identify XBRL tags present in mapping', () => {
      expect(processor.isXBRLTag('test:Revenue', mockXBRLMapping)).to.be.true;
    });

    it('should reject tags not in mapping', () => {
      expect(processor.isXBRLTag('test:UnknownTag', mockXBRLMapping)).to.be.false;
      expect(processor.isXBRLTag('initiativeName', mockXBRLMapping)).to.be.false;
    });

    it('should handle empty mapping', () => {
      expect(processor.isXBRLTag('test:Revenue', {})).to.be.false;
    });
  });

  describe('createXBRLNodeForTag', () => {
    let createXBRLNodeStub: sinon.SinonStub;
    let mockXBRLNode: IXBRLNode;

    beforeEach(() => {
      mockXBRLNode = {
        type: 'ixbrl',
        getTextContent: () => '$1,000',
        getTagName: () => 'ix:nonFraction',
        getName: () => 'test:Revenue',
      } as unknown as IXBRLNode;
      // Stub the createXBRLNode function from lexical/utils
      createXBRLNodeStub = sandbox.stub(lexicalUtils, 'createXBRLNode').returns(mockXBRLNode);
    });

    it('should create XBRL node for valid tag', () => {
      const utrCodeToUtrvMap = createUtrCodeToUtrvMap();
      
      const result = processor.createXBRLNodeForTag(
        'test:Revenue',
        mockXBRLMapping,
        utrCodeToUtrvMap,
        mockTracker
      );

      expect(result).to.equal(mockXBRLNode);
      expect(createXBRLNodeStub.calledOnceWith({
        item: mockXBRLMapping['test:Revenue'],
        mapping: mockXBRLMapping,
        utrCodeToUtrvMap,
        tracker: mockTracker
      })).to.be.true;
    });

    it('should return null for unknown fact name', () => {
      const utrCodeToUtrvMap = createUtrCodeToUtrvMap();
      
      const result = processor.createXBRLNodeForTag(
        'test:UnknownTag',
        mockXBRLMapping,
        utrCodeToUtrvMap,
        mockTracker
      );

      expect(result).to.be.null;
      expect(createXBRLNodeStub.called).to.be.false;
    });
  });

  describe('processTextWithPlaceholders', () => {
    it('should process text with common placeholders', () => {
      const result = runInEditor(() => {
        const text = 'Company {{{initiativeName}}} was established in {{{effectiveDate}}}';
        const utrCodeToUtrvMap = new Map<string, UtrvData>();
        
        return processor.processTextWithPlaceholders(
          text,
          {},
          utrCodeToUtrvMap,
          mockTracker,
          mockReportContext
        );
      });

      expect(result).to.have.length(4);
      expect(result[0]).to.have.property('__text', 'Company ');
      expect(result[1]).to.have.property('__text', 'Test Company');
      expect(result[2]).to.have.property('__text', ' was established in ');
      expect(result[3]).to.have.property('__text', '2023');
    });

    it('should process text with XBRL placeholders', () => {
      const text = 'Revenue: {{{test:Revenue}}}';
      const utrCodeToUtrvMap = createUtrCodeToUtrvMap();
      
      const result = runInEditor(() => {
        // Mock createXBRLNode to return a simple node
        const mockXBRLNode = $createTextNode('$1,000,000') as unknown as IXBRLNode;
        sandbox.stub(lexicalUtils, 'createXBRLNode').returns(mockXBRLNode);
        
        return processor.processTextWithPlaceholders(
          text,
          mockXBRLMapping,
          utrCodeToUtrvMap,
          mockTracker,
          mockReportContext
        );
      });

      expect(result).to.have.length(2);
      expect(result[0]).to.have.property('__text', 'Revenue: ');
      expect(result[1]).to.have.property('__text', '$1,000,000');
    });

    it('should handle mixed placeholder types', () => {
      const text = '{{{initiativeName}}} revenue: {{{test:Revenue}}} in {{{effectiveDate}}}';
      const utrCodeToUtrvMap = createUtrCodeToUtrvMap();
      
      const result = runInEditor(() => {
        // Mock createXBRLNode to return a simple node
        const mockXBRLNode = $createTextNode('$1M') as unknown as IXBRLNode;
        sandbox.stub(lexicalUtils, 'createXBRLNode').returns(mockXBRLNode);
        
        return processor.processTextWithPlaceholders(
          text,
          mockXBRLMapping,
          utrCodeToUtrvMap,
          mockTracker,
          mockReportContext
        );
      });

      expect(result).to.have.length(5);
      expect(result[0]).to.have.property('__text', 'Test Company');
      expect(result[1]).to.have.property('__text', ' revenue: ');
      expect(result[2]).to.have.property('__text', '$1M');
      expect(result[3]).to.have.property('__text', ' in ');
      expect(result[4]).to.have.property('__text', '2023');
    });

    it('should handle unknown placeholders by keeping them as text', () => {
      const result = runInEditor(() => {
        const text = 'Unknown: {{{unknownPlaceholder}}}';
        const utrCodeToUtrvMap = new Map<string, UtrvData>();
        
        return processor.processTextWithPlaceholders(
          text,
          {},
          utrCodeToUtrvMap,
          mockTracker,
          mockReportContext
        );
      });

      expect(result).to.have.length(2);
      expect(result[0]).to.have.property('__text', 'Unknown: ');
      expect(result[1]).to.have.property('__text', '{{{unknownPlaceholder}}}');
    });

    it('should handle XBRL node creation failure gracefully', () => {
      const result = runInEditor(() => {
        const text = 'Revenue: {{{test:Revenue}}}';
        const utrCodeToUtrvMap = createUtrCodeToUtrvMap();
        
        // Mock createXBRLNode to return null (failure case)
        sandbox.stub(lexicalUtils, 'createXBRLNode').returns(null as unknown as IXBRLNode);
        
        return processor.processTextWithPlaceholders(
          text,
          mockXBRLMapping,
          utrCodeToUtrvMap,
          mockTracker,
          mockReportContext
        );
      });

      expect(result).to.have.length(2);
      expect(result[0]).to.have.property('__text', 'Revenue: ');
      expect(result[1]).to.have.property('__text', '{{{test:Revenue}}}'); // Fallback to original text
    });

    it('should handle text without placeholders', () => {
      const result = runInEditor(() => {
        const text = 'This is plain text without any placeholders.';
        const utrCodeToUtrvMap = new Map<string, UtrvData>();
        
        return processor.processTextWithPlaceholders(
          text,
          {},
          utrCodeToUtrvMap,
          mockTracker,
          mockReportContext
        );
      });

      expect(result).to.have.length(1);
      expect(result[0]).to.have.property('__text', 'This is plain text without any placeholders.');
    });

    it('should filter out empty text chunks', () => {
      const result = runInEditor(() => {
        const text = '{{{initiativeName}}}  {{{effectiveDate}}}'; // Extra spaces
        const utrCodeToUtrvMap = new Map<string, UtrvData>();
        
        return processor.processTextWithPlaceholders(
          text,
          {},
          utrCodeToUtrvMap,
          mockTracker,
          mockReportContext
        );
      });

      expect(result).to.have.length(2);
      expect(result[0]).to.have.property('__text', 'Test Company');
      expect(result[1]).to.have.property('__text', '2023');
    });
  });

  describe('getPlaceholderProcessor factory', () => {
    it('should return a singleton instance', () => {
      const instance1 = getPlaceholderProcessor();
      const instance2 = getPlaceholderProcessor();
      
      expect(instance1).to.be.instanceOf(PlaceholderProcessor);
      expect(instance1).to.equal(instance2); // Should be the same instance
    });
  });
});