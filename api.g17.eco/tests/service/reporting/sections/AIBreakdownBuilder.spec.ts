import { expect } from 'chai';
import sinon from 'sinon';
import type { AIBreakdownBuilder } from '../../../../server/service/reporting/sections/AIBreakdownBuilder';
import { AIBreakdownBuilder as AIBreakdownBuilderClass } from '../../../../server/service/reporting/sections/AIBreakdownBuilder';
import type { PlaceholderProcessor } from '../../../../server/service/reporting/sections/PlaceholderProcessor';
import type { SectionBreakdown, ReportContext } from '../../../../server/service/report-document/types';
import type { ExtendedTagMappingItem, XBRLMapping } from '../../../../server/service/reporting/types';
import { ObjectId } from 'bson';
import { ReportDocumentType } from '../../../../server/models/reportDocument';
import type { LexicalRootNode } from '../../../fixtures/xbrlReportFixtures';
import { findHeadingWithText, findParagraphWithText, isListNode } from '../../../fixtures/xbrlReportFixtures';
import { wwgLogger } from '../../../../server/service/wwgLogger';
import type { StubbedInstance } from '../../../utils/test-doubles';
import { createStubbedTestDouble } from '../../../utils/test-doubles';
import { $createTextNode } from 'lexical';

function expectValidRootStructure(result: any): asserts result is { root: LexicalRootNode } {
  expect(result).to.exist;
  expect(result).to.have.property('root');
  expect(result.root).to.have.property('children');
  expect(result.root.children).to.be.an('array');
}

describe('aiBreakdownBuilder', () => {
  const sandbox = sinon.createSandbox();
  let aiBreakdownBuilder: AIBreakdownBuilder;
  let mockPlaceholderProcessor: StubbedInstance<PlaceholderProcessor>;

  beforeEach(() => {
    mockPlaceholderProcessor = createStubbedTestDouble<PlaceholderProcessor>({
      processTextWithPlaceholders: sandbox.stub(),
      splitTextIntoChunks: sandbox.stub(),
      isPlaceholder: sandbox.stub(),
      extractPlaceholderName: sandbox.stub(),
      isCommonPlaceholder: sandbox.stub(),
      getCommonPlaceholderValue: sandbox.stub(),
      isXBRLTag: sandbox.stub(),
      createXBRLNodeForTag: sandbox.stub(),
    });

    // Default behavior: return nodes with the original text
    mockPlaceholderProcessor.processTextWithPlaceholders.callsFake((text: string) => {
      return [text].map((t: string) => $createTextNode(t));
    });

    // Create test instance with mocked dependencies
    aiBreakdownBuilder = new AIBreakdownBuilderClass(
      wwgLogger,
      mockPlaceholderProcessor as unknown as PlaceholderProcessor
    );
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('AIBreakdownBuilder.buildLexicalState', () => {
    const mockReportId = new ObjectId();
    const mockReportType = ReportDocumentType.ISSB;

    const mockReportContext: ReportContext = {
      initiativeName: 'Test Initiative',
      effectiveDate: '2024-01-01',
    };

    const mockSectionBreakdown: SectionBreakdown = {
      introduction: 'This is the introduction',
      summary: 'This is the summary',
      conclusion: 'This is the conclusion',
      xbrlTagSections: [
        {
          title: 'Tag Section 1',
          description: 'Tag section description',
          keyHighlights: ['Highlight 1', 'Highlight 2'],
        },
      ],
    };

    const mockRelevantMapping: XBRLMapping<ExtendedTagMappingItem> = {
      'test:FactName': {
        factName: 'test:FactName',
        utrCode: 'TEST001',
        valueListCode: 'VL001',
        utrvId: new ObjectId(),
      },
    };

    it('should build lexical state with valid parameters', async () => {
      const params = {
        reportId: mockReportId,
        reportType: mockReportType,
        reportContext: mockReportContext,
        sectionsData: {
          riskManagement: {
            relevantMapping: mockRelevantMapping,
            breakdown: mockSectionBreakdown,
            utrvData: [],
          },
        },
      };

      const result = await aiBreakdownBuilder.buildLexicalState(params);

      expectValidRootStructure(result);

      // Should have at least 5 nodes: heading, introduction, summary, xbrl section, conclusion
      expect(result.root.children.length).to.be.greaterThan(4);

      // Should contain introduction, summary, and conclusion text from our breakdown
      const introNode = findParagraphWithText(result.root.children, 'This is the introduction');
      expect(introNode).to.exist;

      const summaryNode = findParagraphWithText(result.root.children, 'This is the summary');
      expect(summaryNode).to.exist;

      const conclusionNode = findParagraphWithText(result.root.children, 'This is the conclusion');
      expect(conclusionNode).to.exist;
    });

    it('should handle empty sections', async () => {
      const params = {
        reportId: mockReportId,
        reportType: mockReportType,
        reportContext: mockReportContext,
        sectionsData: {},
      };

      const result = await aiBreakdownBuilder.buildLexicalState(params);

      expectValidRootStructure(result);
      // With empty sectionsData, it should still render static sections from the report outline
      expect(result.root.children.length).to.be.greaterThan(0);
    });

    it('should handle sections without breakdown data', async () => {
      const params = {
        reportId: mockReportId,
        reportType: mockReportType,
        reportContext: mockReportContext,
        sectionsData: {
          riskManagement: undefined,
        },
      };

      const result = await aiBreakdownBuilder.buildLexicalState(params);

      expectValidRootStructure(result);

      // Should fallback to static section with heading, description, and list
      expect(result.root.children.length).to.be.greaterThan(1);

      // Should contain description paragraph from static section
      const descriptionNode = findParagraphWithText(result.root.children, 'processes the company uses to identify');
      expect(descriptionNode).to.exist;

      // Should contain numbered list for subsections
      const listNode = result.root.children.find(isListNode);
      expect(listNode).to.exist;
      expect(listNode).to.have.property('listType', 'number');
    });

    it('should handle sections with breakdown but no utrv data', async () => {
      const params = {
        reportId: mockReportId,
        reportType: mockReportType,
        reportContext: mockReportContext,
        sectionsData: {
          riskManagement: {
            relevantMapping: {},
            breakdown: mockSectionBreakdown,
            utrvData: [],
          },
        },
      };

      const result = await aiBreakdownBuilder.buildLexicalState(params);

      expectValidRootStructure(result);

      // Should still build full breakdown structure even without UTRV data
      expect(result.root.children.length).to.be.greaterThan(4);

      // Should contain all breakdown content
      const introNode = findParagraphWithText(result.root.children, 'This is the introduction');
      expect(introNode).to.exist;

      const summaryNode = findParagraphWithText(result.root.children, 'This is the summary');
      expect(summaryNode).to.exist;

      const conclusionNode = findParagraphWithText(result.root.children, 'This is the conclusion');
      expect(conclusionNode).to.exist;
    });

    it('should delegate placeholder processing to PlaceholderProcessor', async () => {
      const params = {
        reportId: mockReportId,
        reportType: mockReportType,
        reportContext: mockReportContext,
        sectionsData: {
          riskManagement: {
            relevantMapping: mockRelevantMapping,
            breakdown: mockSectionBreakdown,
            utrvData: [],
          },
        },
      };

      await aiBreakdownBuilder.buildLexicalState(params);

      // Verify that PlaceholderProcessor was called for each text field in the breakdown
      expect(mockPlaceholderProcessor.processTextWithPlaceholders.callCount).to.be.greaterThan(0);

      // Check specific calls for breakdown fields
      const callArgs = mockPlaceholderProcessor.processTextWithPlaceholders.getCalls().map((call) => call.args[0]);
      expect(callArgs).to.include('This is the introduction');
      expect(callArgs).to.include('This is the summary');
      expect(callArgs).to.include('This is the conclusion');
      expect(callArgs).to.include('Tag Section 1');
      expect(callArgs).to.include('Tag section description');
      expect(callArgs).to.include('Highlight 1');
      expect(callArgs).to.include('Highlight 2');
    });

    it('should handle multiple sections', async () => {
      const secondBreakdown: SectionBreakdown = {
        introduction: 'Second introduction',
        summary: 'Second summary',
        conclusion: 'Second conclusion',
        xbrlTagSections: [],
      };

      const params = {
        reportId: mockReportId,
        reportType: mockReportType,
        reportContext: mockReportContext,
        sectionsData: {
          riskManagement: {
            relevantMapping: mockRelevantMapping,
            breakdown: mockSectionBreakdown,
            utrvData: [],
          },
          metricsAndTargets: {
            relevantMapping: {},
            breakdown: secondBreakdown,
            utrvData: [],
          },
        },
      };

      const result = await aiBreakdownBuilder.buildLexicalState(params);

      expectValidRootStructure(result);

      // Should contain nodes from both sections
      expect(result.root.children.length).to.be.greaterThan(6);

      // Should contain risk management section heading (partial match)
      const riskManagementSectionHeading = findHeadingWithText(result.root.children, 'Risk Management');
      expect(riskManagementSectionHeading).to.exist;

      // Should contain content from both sections
      const firstIntroNode = findParagraphWithText(result.root.children, 'This is the introduction');
      expect(firstIntroNode).to.exist;

      const secondIntroNode = findParagraphWithText(result.root.children, 'Second introduction');
      expect(secondIntroNode).to.exist;
    });

    it('should handle sections with empty xbrlTagSections', async () => {
      const breakdownWithEmptyTags: SectionBreakdown = {
        introduction: 'Introduction',
        summary: 'Summary',
        conclusion: 'Conclusion',
        xbrlTagSections: [],
      };

      const params = {
        reportId: mockReportId,
        reportType: mockReportType,
        reportContext: mockReportContext,
        sectionsData: {
          riskManagement: {
            relevantMapping: mockRelevantMapping,
            breakdown: breakdownWithEmptyTags,
            utrvData: [],
          },
        },
      };

      const result = await aiBreakdownBuilder.buildLexicalState(params);

      expectValidRootStructure(result);

      // Should have many nodes from all report sections, but this specific section should have no XBRL subsections
      expect(result.root.children.length).to.be.greaterThan(4);

      // Should contain our breakdown content somewhere in the children
      const introNode = findParagraphWithText(result.root.children, 'Introduction');
      expect(introNode).to.exist;
    });

    it('should handle breakdown with multiple xbrlTagSections', async () => {
      const breakdownWithMultipleTags: SectionBreakdown = {
        introduction: 'Introduction',
        summary: 'Summary',
        conclusion: 'Conclusion',
        xbrlTagSections: [
          {
            title: 'First Tag Section',
            description: 'First description',
            keyHighlights: ['First highlight'],
          },
          {
            title: 'Second Tag Section',
            description: 'Second description',
            keyHighlights: ['Second highlight', 'Third highlight'],
          },
        ],
      };

      const params = {
        reportId: mockReportId,
        reportType: mockReportType,
        reportContext: mockReportContext,
        sectionsData: {
          riskManagement: {
            relevantMapping: mockRelevantMapping,
            breakdown: breakdownWithMultipleTags,
            utrvData: [],
          },
        },
      };

      const result = await aiBreakdownBuilder.buildLexicalState(params);

      expectValidRootStructure(result);

      // Should have many nodes from all report sections
      expect(result.root.children.length).to.be.greaterThan(10);

      // Should contain both XBRL subsection headings
      const firstTagHeading = findHeadingWithText(result.root.children, 'First Tag Section', 'h3');
      expect(firstTagHeading).to.exist;

      const secondTagHeading = findHeadingWithText(result.root.children, 'Second Tag Section', 'h3');
      expect(secondTagHeading).to.exist;

      // Should contain both description paragraphs
      const firstDescription = findParagraphWithText(result.root.children, 'First description');
      expect(firstDescription).to.exist;

      // Should contain numbered lists for key highlights from our breakdown
      const lists = result.root.children.filter(isListNode);
      expect(lists.length).to.be.greaterThan(0);
    });

    it('should work with different report types', async () => {
      const params = {
        reportId: mockReportId,
        reportType: ReportDocumentType.CSRD,
        reportContext: mockReportContext,
        sectionsData: {
          generalInformation: {
            relevantMapping: mockRelevantMapping,
            breakdown: mockSectionBreakdown,
            utrvData: [],
          },
        },
      };

      const result = await aiBreakdownBuilder.buildLexicalState(params);

      expectValidRootStructure(result);

      // Should build the same structure regardless of report type
      expect(result.root.children.length).to.be.greaterThan(4);

      // Should still contain section heading for General Information
      const sectionHeading = findHeadingWithText(result.root.children, 'General Information', 'h2');
      expect(sectionHeading).to.exist;

      // Should contain breakdown content
      const introNode = findParagraphWithText(result.root.children, 'This is the introduction');
      expect(introNode).to.exist;
    });
  });
});
