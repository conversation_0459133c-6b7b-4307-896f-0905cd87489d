/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { wwgLogger, type LoggerInterface } from '../wwgLogger';
import ReportDocument, {
  ReportDocumentStatus,
  ReportDocumentTemplate,
  type CreateReportDocument,
  type ReportDocumentPlain,
  type ReportDocumentModel,
} from '../../models/reportDocument';
import { ObjectId } from 'bson';
import { getReportingFactory } from '../reporting/ReportingFactory';
import type { UserPlain } from '../../models/user';
import type { SerializedEditorState } from 'lexical';
import type { AIReportDocumentJobModel, InitializeLexicalState, SetupIXBRLReportTask } from './types';
import BackgroundJob, { JobType, TaskStatus, TaskType, type CreateJob } from '../../models/backgroundJob';
import { createLogEntry, finalStatuses } from '../jobs';
import { generatedUUID } from '../crypto/token';
import { getBackgroundJobService, type BackgroundJobService } from '../background-process/BackgroundJobService';
import ContextError from '../../error/ContextError';

export class ReportDocumentManager {
  constructor(
    private readonly logger: LoggerInterface,
    private reportFactory: ReturnType<typeof getReportingFactory>,
    private backgroundJobModel: typeof BackgroundJob,
    private bgJobService: BackgroundJobService
  ) {}

  public async create(createData: CreateReportDocument) {
    const document = new ReportDocument(createData);
    return document.save();
  }

  /**
   * Handles reports that are not in pending status
   */
  private async handleNonPendingReport(report: ReportDocumentModel): Promise<InitializeLexicalState> {
    if (report.config?.template !== ReportDocumentTemplate.AiGenerated) {
      return {
        status: report.status,
      };
    }

    const job = await this.backgroundJobModel
      .findOne({
        idempotencyKey: this.getIdempotencyKey({
          initiativeId: report.initiativeId,
          reportId: report._id,
        }),
        initiativeId: report.initiativeId,
        type: JobType.AIReportDocument,
      })
      .lean<AIReportDocumentJobModel>()
      .exec();

    const lexicalState = job?.tasks.find((t) => t.type === TaskType.GenerateReportLexicalState)?.data?.lexicalState;

    return {
      status: report.status,
      lexicalState,
    };
  }

  /**
   * Handles pending reports based on their template configuration
   */
  private async handlePendingReportByTemplate(
    report: ReportDocumentModel,
    user: UserPlain
  ): Promise<InitializeLexicalState> {
    const template = report.config?.template;

    switch (template) {
      case ReportDocumentTemplate.Simple:
        return this.handleSimpleTemplate(report, user);
      case ReportDocumentTemplate.AiGenerated:
        return this.handleAiGeneratedTemplate(report);
      case ReportDocumentTemplate.Blank:
      default:
        return {
          status: report.status,
        };
    }
  }

  /**
   * Handles simple template generation
   */
  private async handleSimpleTemplate(report: ReportDocumentModel, user: UserPlain): Promise<InitializeLexicalState> {
    const generator = this.reportFactory.getLexicalStateGenerator(report.type);
    const templateLexicalState = await generator.getTemplate({
      initiativeId: report.initiativeId,
      user,
      surveyIds: report.surveyIds,
    });

    report.status = ReportDocumentStatus.Generated;
    await report.save();

    return {
      status: report.status,
      lexicalState: templateLexicalState,
    };
  }

  /**
   * Handles AI-generated template by creating a background job
   */
  private async handleAiGeneratedTemplate(report: ReportDocumentModel): Promise<InitializeLexicalState> {
    await this.upsertJob(report);

    return {
      status: report.status,
    };
  }

  public async initializeLexicalStateFromTemplate({
    reportId,
    user,
  }: {
    reportId: string;
    user: UserPlain;
  }): Promise<InitializeLexicalState> {
    const report = await ReportDocument.findById(reportId).orFail().exec();

    // Handle non-pending reports
    if (report.status !== ReportDocumentStatus.Pending) {
      return this.handleNonPendingReport(report);
    }

    // Handle pending reports based on template type
    return this.handlePendingReportByTemplate(report, user);
  }

  public async get({
    reportId,
    initiativeId,
  }: {
    reportId: string;
    initiativeId: string;
  }): Promise<ReportDocumentPlain> {
    return ReportDocument.findOne({
      _id: new ObjectId(reportId),
      initiativeId: new ObjectId(initiativeId),
    })
      .orFail()
      .lean()
      .exec();
  }

  public async list({ initiativeId }: { initiativeId: string }): Promise<ReportDocumentPlain[]> {
    return ReportDocument.find({
      initiativeId: new ObjectId(initiativeId),
    })
      .lean()
      .exec();
  }

  public async update({
    reportId,
    initiativeId,
    ...payload
  }: {
    reportId: string;
    initiativeId: string;
    [key: string]: any;
  }): Promise<ReportDocumentPlain> {
    return ReportDocument.findOneAndUpdate(
      {
        _id: new ObjectId(reportId),
        initiativeId: new ObjectId(initiativeId),
      },
      {
        $set: payload,
      },
      { new: true }
    )
      .orFail()
      .exec();
  }

  public async deleteReport({ reportId, initiativeId }: { reportId: string; initiativeId: string }) {
    this.logger.warn('Deleting report document', { reportId, initiativeId });

    return ReportDocument.findOneAndDelete({
      initiativeId: new ObjectId(initiativeId),
      _id: new ObjectId(reportId),
    })
      .orFail()
      .exec();
  }

  public async getTemplate({ reportId, user }: { reportId: string; user: UserPlain }) {
    const reportDocument = await ReportDocument.findById(new ObjectId(reportId)).orFail().lean().exec();

    const generator = this.reportFactory.getLexicalStateGenerator(reportDocument.type);
    return generator.getTemplate({
      initiativeId: reportDocument.initiativeId,
      user,
      surveyIds: reportDocument.surveyIds,
    });
  }

  public async download({ reportId, editorState }: { reportId: string; editorState: SerializedEditorState }) {
    const reportDocument = await ReportDocument.findById(new ObjectId(reportId)).orFail().lean().exec();

    const generator = this.reportFactory.getLexicalStateGenerator(reportDocument.type);
    return generator.downloadReport({
      reportDocument,
      editorState,
    });
  }

  public async updateReportStatus({ reportId, status }: { reportId: ObjectId; status: ReportDocumentStatus }) {
    return ReportDocument.findOneAndUpdate(
      {
        _id: reportId,
      },
      {
        $set: { status },
      },
      { new: true }
    )
      .orFail()
      .exec();
  }

  public async synchronizeDocumentReport(reportId: string) {
    const reportDocument = await ReportDocument.findById(reportId).orFail().exec();
    if (reportDocument.status !== ReportDocumentStatus.Generated) {
      return reportDocument;
    }

    reportDocument.status = ReportDocumentStatus.Completed;
    await reportDocument.save();

    return reportDocument;
  }

  private getIdempotencyKey(context: { initiativeId: ObjectId; reportId: ObjectId }) {
    return `initiativeId:${context.initiativeId}-reportId:${context.reportId}`;
  }

  private findProcessingJob({ initiativeId, reportId }: { initiativeId: ObjectId; reportId: ObjectId }) {
    return this.backgroundJobModel
      .findOne({
        idempotencyKey: this.getIdempotencyKey({ initiativeId, reportId }),
        initiativeId: new ObjectId(initiativeId),
        type: JobType.AIReportDocument,
        status: { $nin: finalStatuses },
      })
      .lean<AIReportDocumentJobModel>()
      .exec();
  }

  public async upsertJob(data: ReportDocumentPlain): Promise<AIReportDocumentJobModel> {
    const { initiativeId, createdBy, type: reportType, _id: reportId } = data;

    const jobType = JobType.AIReportDocument;
    const existingJob = await this.findProcessingJob({ initiativeId, reportId });
    if (existingJob) {
      return existingJob;
    }

    this.logger.info(`Creating ${jobType} job for initiativeId: ${initiativeId}`);

    const setupTask: SetupIXBRLReportTask = {
      id: generatedUUID(),
      name: `Setup ixbrl ${reportType} report`,
      type: TaskType.SetupIXBRLReport,
      status: TaskStatus.Pending,
      data: {
        reportType,
        reportId,
      },
    };

    const createData: CreateJob = {
      idempotencyKey: this.getIdempotencyKey({ initiativeId, reportId }),
      type: jobType,
      name: 'Generating Report Document',
      initiativeId,
      userId: createdBy,
      tasks: [setupTask],
      logs: [createLogEntry(`${jobType} job created for initiative ${initiativeId}`)],
    };

    const job = (await this.backgroundJobModel.create(createData)) as unknown as AIReportDocumentJobModel;
    this.bgJobService.runFromJob(job).catch((e) => {
      this.logger.error(
        new ContextError(`Failed to complete background job type ${job.type}`, {
          debugMessage: `Job "${job.name}" failed to complete`,
          jobId: job._id,
          jobType: job.type,
          cause: e,
        })
      );
    });

    return job;
  }
}

let instance: ReportDocumentManager;
export const getReportDocumentManager = () => {
  if (!instance) {
    instance = new ReportDocumentManager(wwgLogger, getReportingFactory(), BackgroundJob, getBackgroundJobService());
  }
  return instance;
};
