import { createHeadlessEditor } from '@lexical/headless';
import { $createParagraphNode, $createTextNode, $getRoot } from 'lexical';
import { XbrlTracker } from '../XbrlTracker';
import { editorConfig, getReportOutline } from '../utils';
import type { OutlineSection } from './utils';
import type { ObjectId } from 'bson';
import type { ReportContext, SectionBreakdown, SectionData } from '../../report-document/types';
import { $createHeadingNode } from '@lexical/rich-text';
import type { ExtendedTagMappingItem, XBRLMapping, UtrvData } from '../types';
import { buildStaticSection } from './staticBuilder';
import { $createListItemNode, $createListNode } from '@lexical/list';
import type { LexicalNode } from 'lexical/LexicalNode';
import type { ReportDocumentType } from '../../../models/reportDocument';
import { wwgLogger, type LoggerInterface } from '../../wwgLogger';
import type { PlaceholderProcessor } from './PlaceholderProcessor';
import { getPlaceholderProcessor } from './PlaceholderProcessor';

interface BuildAIBreakdownLexicalStateParams {
  reportId: ObjectId;
  reportType: ReportDocumentType;
  sectionsData: Record<string, SectionData | undefined>;
  reportContext: ReportContext;
}

interface BuildSectionParams {
  tracker: XbrlTracker;
  outline: OutlineSection;
  breakdown?: SectionBreakdown;
  relevantMapping?: XBRLMapping<ExtendedTagMappingItem>;
  utrvData?: UtrvData[];
  reportContext: ReportContext;
}

export class AIBreakdownBuilder {
  constructor(private readonly logger: LoggerInterface, private readonly placeholderProcessor: PlaceholderProcessor) {}

  public async buildLexicalState(params: BuildAIBreakdownLexicalStateParams) {
    const { reportType, sectionsData, reportContext } = params;
    const sections = getReportOutline(reportType);

    const tracker = new XbrlTracker();

    // Use Lexical headless editor to build the state
    const editor = createHeadlessEditor(editorConfig);
    editor.update(
      () => {
        $getRoot().append(
          ...Object.entries(sections).flatMap(([sectionKey, outline]) => {
            const { relevantMapping, breakdown, utrvData } = sectionsData[sectionKey] || {};
            return this.buildSection({ tracker, outline, breakdown, relevantMapping, utrvData, reportContext });
          })
        );
      },
      { discrete: true }
    );

    return editor.getEditorState().toJSON();
  }

  private buildSection(params: BuildSectionParams): LexicalNode[] {
    const { tracker, outline, breakdown, relevantMapping = {}, utrvData = [], reportContext } = params;

    if (!breakdown) {
      this.logger.info('No breakdown data for this section, falling back to static section', {
        section: outline.heading,
      });
      return buildStaticSection({ section: outline });
    }

    this.logger.info('Generate section using AI breakdown', {
      section: outline.heading,
    });
    // Create mapping and UTRV map for XBRL node creation
    const utrCodeToUtrvMap = new Map<string, UtrvData>();
    utrvData.forEach((utrv) => {
      utrCodeToUtrvMap.set(utrv.universalTracker.code, utrv);
    });

    const nodes = [];

    const sectionHeading = $createHeadingNode('h2');
    sectionHeading.append($createTextNode(`📑 ${outline.heading}`));
    nodes.push(sectionHeading);

    const introductionParagraph = $createParagraphNode();
    const introNodes = this.placeholderProcessor.processTextWithPlaceholders(
      breakdown.introduction,
      relevantMapping,
      utrCodeToUtrvMap,
      tracker,
      reportContext
    );
    introductionParagraph.append(...introNodes);
    nodes.push(introductionParagraph);

    const summaryParagraph = $createParagraphNode();
    const summaryNodes = this.placeholderProcessor.processTextWithPlaceholders(
      breakdown.summary,
      relevantMapping,
      utrCodeToUtrvMap,
      tracker,
      reportContext
    );
    summaryParagraph.append(...summaryNodes);
    nodes.push(summaryParagraph);

    breakdown.xbrlTagSections.forEach(({ title, description, keyHighlights }) => {
      const subsectionHeading = $createHeadingNode('h3');
      const titleNodes = this.placeholderProcessor.processTextWithPlaceholders(
        title,
        relevantMapping,
        utrCodeToUtrvMap,
        tracker,
        reportContext
      );
      subsectionHeading.append(...titleNodes);

      const subsectionDescription = $createParagraphNode();
      const descriptionNodes = this.placeholderProcessor.processTextWithPlaceholders(
        description,
        relevantMapping,
        utrCodeToUtrvMap,
        tracker,
        reportContext
      );
      subsectionDescription.append(...descriptionNodes);

      const list = $createListNode('number');
      keyHighlights.forEach((highlight) => {
        const listItem = $createListItemNode();
        const paragraph = $createParagraphNode();
        const highlightNodes = this.placeholderProcessor.processTextWithPlaceholders(
          highlight,
          relevantMapping,
          utrCodeToUtrvMap,
          tracker,
          reportContext
        );
        paragraph.append(...highlightNodes);
        listItem.append(paragraph);
        list.append(listItem);
      });

      nodes.push(subsectionHeading, subsectionDescription, list);
    });

    const conclusionParagraph = $createParagraphNode();
    const conclusionNodes = this.placeholderProcessor.processTextWithPlaceholders(
      breakdown.conclusion,
      relevantMapping,
      utrCodeToUtrvMap,
      tracker,
      reportContext
    );
    conclusionParagraph.append(...conclusionNodes);
    nodes.push(conclusionParagraph);

    return nodes;
  }
}

let instance: AIBreakdownBuilder | undefined;
export const getAIBreakdownBuilder = (): AIBreakdownBuilder => {
  if (!instance) {
    instance = new AIBreakdownBuilder(wwgLogger, getPlaceholderProcessor());
  }
  return instance;
};
