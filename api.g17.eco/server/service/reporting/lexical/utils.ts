import { $createListItemNode, $createListNode } from '@lexical/list';
import { $createParagraphNode, $createTextNode } from 'lexical';
import type { SectionData } from '../xhtml/types';
import { getData } from '../utils';
import { UtrValueType } from '../../../models/public/universalTrackerType';
import { $createIxbrlNode, type IXBRLNode } from './nodes/IXBRLNode';
import { NumberScale } from '../../../types/units';
import { numberScaleValueMap } from '../../units/unitTypes';
import type { TagMappingItem, UtrvData, XBRLMapping } from '../types';
import type { XbrlTracker } from '../XbrlTracker';

export const createListNode = (items: string[]) => {
  const list = $createListNode('number');
  items.forEach((itemText) => {
    const listItem = $createListItemNode();
    const paragraph = $createParagraphNode();
    paragraph.append($createTextNode(itemText));
    listItem.append(paragraph);
    list.append(listItem);
  });
  return list;
};

export const createXBRLNode = <T extends TagMappingItem>({
  item,
  mapping,
  utrCodeToUtrvMap,
  tracker,
}: {
  item: T;
  mapping: XBRLMapping<T>;
  utrCodeToUtrvMap: Map<string, UtrvData>;
  tracker: XbrlTracker;
}): IXBRLNode => {
  const utrv = utrCodeToUtrvMap.get(item.utrCode);
  const value = getData({ factName: item.factName, mapping, utrCodeToUtrvMap, fallback: '-' }); // Use fallback if no UTRV data

  let tag: 'ix:nonFraction' | 'ix:nonNumeric' = 'ix:nonFraction';
  let format: string | undefined = undefined;
  let decimals: number | undefined = undefined;
  let scale: number | undefined = undefined;
  let unitRef: string | undefined = undefined;

  if (utrv) {
    const valueType = utrv.universalTracker.valueType;
    if (
      valueType === UtrValueType.Number ||
      valueType === UtrValueType.Percentage ||
      valueType === UtrValueType.NumericValueList
    ) {
      // Numeric values - use proper formatting, decimals, scale, and units
      format = 'ixt4:num-dot-decimal';
      decimals = 2;
      scale = numberScaleValueMap[utrv.universalTracker.numberScale ?? NumberScale.Single];
      unitRef = utrv.universalTracker.unit ? tracker.addUnitRef(utrv.universalTracker.unit) : undefined;
    } else {
      // Non-numeric values - use text format
      tag = 'ix:nonNumeric';
      format = undefined;
      scale = undefined;
      unitRef = undefined;
    }
  }

  const ixbrlNode = $createIxbrlNode({
    tag,
    name: item.factName,
    format,
    decimals,
    scale,
    unitRef: unitRef ?? '',
    contextRef: tracker.getContextId(),
    factId: tracker.getFactId(),
  });

  ixbrlNode.append($createTextNode(String(value)));
  return ixbrlNode;
};

export const buildXBRLNodes = ({
  items,
  mapping,
  utrCodeToUtrvMap,
  tracker,
}: Pick<SectionData, 'mapping' | 'utrCodeToUtrvMap' | 'tracker'> & { items: TagMappingItem[] }) => {
  return items.map((item) => {
    const ixbrlNode = createXBRLNode({ item, mapping, utrCodeToUtrvMap, tracker });
    return { factName: item.factName, ixbrlNode };
  });
};
