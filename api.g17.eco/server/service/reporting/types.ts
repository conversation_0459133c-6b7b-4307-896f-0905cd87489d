import type { InitiativePlain } from '../../models/initiative';
import type { XbrlTracker } from './XbrlTracker';
import type { SurveyModelPlain } from '../../models/survey';
import type { UniversalTrackerValuePlain } from '../../models/universalTrackerValue';
import type { UniversalTrackerPlain } from '../../models/universalTracker';
import type { ObjectId } from 'bson';
import type { SerializedEditorState } from 'lexical';
import type { UserPlain } from './../../models/user';
import type { ReportDocumentPlain } from './../../models/reportDocument';

export interface Languages {
  en: string;
}

export interface Labels {
  std: Languages;
  doc?: Languages;
  [k: `ns${number}`]: Languages | undefined;
}

/**
 * @example
 * {
 *   "esrs:DescriptionOfMaterialImpactsResultingFromMaterialityAssessmentExplanatory": {
 *     "labels": {
 *       "std": {
 *         "en": "Description of material impacts resulting from materiality assessment [text block]"
 *       }
 *     },
 *     "r": [
 *       [
 *         ["Name", "ESRS"],
 *         ["Number", "ESRS 2"],
 *         ["Paragraph", "48"],
 *         ["Subparagraph", "a"],
 *         ["Section", "SBM-3"]
 *       ],
 *       [
 *         ["Name", "ESRS"],
 *         ["Number", "E4"],
 *         ["Paragraph", "16"],
 *         ["Section", "SBM-3"]
 *       ]
 *     ],
 *     "t": true
 *   }
 * }
 */
export interface Concept {
  labels: Labels;
  /** d: "t" not sure what this means for now  */
  d?: 't' | 'e' | number;
  r?: ReferenceRow[];
  t?: boolean;
  en?: boolean;
}

export type ReferenceItem = [label: string, value: string];
/**
 * @example
 *  [
 *    ["Name", "ESRS"],
 *    ["Number", "ESRS 2"],
 *    ["Paragraph", "48"],
 *    ["Subparagraph", "a"],
 *    ["Section", "SBM-3"]
 *  ]
 */
export type ReferenceRow = ReferenceItem[];

export interface DefinitionMapItem {
  label: string;
  technicalName: string;
  references?: ReferenceRow[];
}

export interface GenerateLexicalStateParams
  extends Pick<GeneratorParameters, 'initiative' | 'survey' | 'mapping' | 'utrCodeToUtrvMap'> {
  user: Pick<UserPlain, '_id'>;
  preview?: boolean;
}

export interface TagMappingItem {
  factName: string;
  utrCode: string;
  valueListCode?: string;
}

export interface ExtendedTagMappingItem extends TagMappingItem {
  utrvId: ObjectId | undefined;
}

export interface XBRLMapping<T extends TagMappingItem = TagMappingItem> {
  [key: string]: T | undefined;
}

type BaseUtrv = Pick<UniversalTrackerValuePlain, '_id' | 'effectiveDate' | 'value' | 'valueData' | 'status'>;
export interface UtrvData extends BaseUtrv {
  universalTracker: UniversalTrackerPlain;
}

export interface ILexicalStateGenerator {
  getTemplate(params: {
    initiativeId: ObjectId;
    user: UserPlain;
    reportType: string;
    surveyId?: ObjectId;
  }): Promise<SerializedEditorState>;
  downloadReport(params: {
    reportDocument: ReportDocumentPlain;
    editorState: SerializedEditorState;
    preview: boolean;
  }): Promise<{ filename: string; xmlString: string; tracker: XbrlTracker }>;
}

export interface GeneratorParameters {
  initiative: Pick<InitiativePlain, '_id' | 'name'>;
  survey: SurveyModelPlain;
  mapping: XBRLMapping;
  utrvData: UtrvData[];
  utrCodeToUtrvMap: Map<string, UtrvData>;
}

export type ReportDefinition = Record<string, DefinitionMapItem | undefined>;
