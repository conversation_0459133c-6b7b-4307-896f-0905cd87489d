/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { z } from 'zod';
import { ReportDocumentTemplate, ReportDocumentType } from '../../models/reportDocument';
import { refineIdSchema } from './common';

export const createReportDocumentSchema = z.object({
  title: z.string(),
  description: z.string().optional(),
  type: z.nativeEnum(ReportDocumentType),
  config: z
    .object({
      template: z.nativeEnum(ReportDocumentTemplate),
    })
    .optional(),
  surveyId: refineIdSchema('surveyId').optional(),
});

export const updateReportDocumentSchema = createReportDocumentSchema.omit({ type: true, surveyId: true, config: true });
