import { describe, it, expect, beforeAll, afterEach, afterAll, vi } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import { setupServer } from 'msw/node';
import { http } from 'msw';
import selectEvent from 'react-select-event';
import { ReportDocumentCreate } from './ReportDocumentCreate';
import { getUrl, setup } from '@fixtures/utils';
import { reduxFixtureStore } from '@fixtures/redux-store';
import { ResponseSuccess, ResponseError } from '@fixtures/msw-fixtures';
import { ReportDocumentTemplate, ReportDocument, ReportDocumentStatus } from '@g17eco/types/reportDocument';
import { SurveyType } from '@g17eco/types/surveyCommon';
import { createSurveyListItem } from '@fixtures/survey-factory';

vi.mock('@g17eco/molecules/toasts', () => ({
  generateErrorToast: vi.fn(),
  generateToast: vi.fn(),
}));

describe('ReportDocumentCreate', () => {
  const server = setupServer();

  beforeAll(() => server.listen());
  afterEach(() => {
    server.resetHandlers();
    vi.clearAllMocks();
  });
  afterAll(() => server.close());

  const defaultProps = {
    initiativeId: 'test-initiative-id',
    type: 'csrd',
    onCancel: vi.fn(),
    onView: vi.fn(),
  };

  const mockReportDocument: ReportDocument = {
    _id: 'test-report-id',
    title: 'Existing Report',
    description: 'Existing description',
    initiativeId: 'test-initiative-id',
    type: 'csrd',
    config: {
      template: ReportDocumentTemplate.Simple,
    },
    status: ReportDocumentStatus.Completed,
    created: new Date().toISOString(),
    createdBy: 'user-1',
    lastUpdated: new Date().toISOString(),
  };

  const mockSurveys = [
    createSurveyListItem({
      _id: 'survey-1',
      name: 'Environmental Survey 2024',
      type: SurveyType.Default,
      effectiveDate: '2024-01-01',
      completedDate: '2024-06-15',
    }),
    createSurveyListItem({
      _id: 'survey-2',
      name: 'Social Impact Assessment',
      type: SurveyType.Aggregation,
      effectiveDate: '2023-12-15',
      completedDate: undefined,
    }),
    createSurveyListItem({
      _id: 'survey-3',
      name: 'Governance Review',
      type: SurveyType.Default,
      effectiveDate: '2024-02-01',
      completedDate: undefined,
    }),
  ];

  const renderComponent = (props = {}, options = {}) => {
    // Set up default survey API handler
    server.use(http.get(getUrl('initiatives/test-initiative-id/surveys'), async () => ResponseSuccess(mockSurveys)));

    return setup(<ReportDocumentCreate {...defaultProps} {...props} />, {
      store: reduxFixtureStore(),
      ...options,
    });
  };

  describe('rendering', () => {
    it('renders form with correct fields for new report', async () => {
      renderComponent();

      expect(screen.getByPlaceholderText(/title/i)).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/description/i)).toBeInTheDocument();
      expect(screen.getByText('Blank')).toBeInTheDocument(); // Template field
      expect(screen.getByRole('button', { name: /create report/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /back/i })).toBeInTheDocument();

      // Survey field should be present after API loads
      expect(await screen.findByTestId('survey-select')).toBeInTheDocument();
    });

    it('renders form with existing report data when editing', () => {
      renderComponent({ reportDocument: mockReportDocument });

      expect(screen.getByDisplayValue('Existing Report')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Existing description')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /save/i })).toBeInTheDocument();
    });

    it('displays correct header based on report type', () => {
      renderComponent();

      expect(
        screen.getByRole('heading', { name: /CSRD \(Corporate Sustainability Reporting Directive\) report/i }),
      ).toBeInTheDocument();
    });
  });

  describe('form validation', () => {
    it('disables submit button when title is empty', () => {
      renderComponent();

      const submitButton = screen.getByRole('button', { name: /create report/i });
      expect(submitButton).toBeDisabled();
    });

    it('enables submit button when form is valid', async () => {
      const { user } = renderComponent();

      await user.type(screen.getByPlaceholderText(/title/i), 'Test Report');

      const submitButton = screen.getByRole('button', { name: /create report/i });
      expect(submitButton).toBeEnabled();
    });
  });

  describe('form interactions', () => {
    it('updates title field when typing', async () => {
      const { user } = renderComponent();

      const titleInput = screen.getByPlaceholderText(/title/i);
      await user.type(titleInput, 'New Report Title');

      expect(titleInput).toHaveValue('New Report Title');
    });

    it('updates description field when typing', async () => {
      const { user } = renderComponent();

      const descriptionInput = screen.getByPlaceholderText(/description/i);
      await user.type(descriptionInput, 'New report description');

      expect(descriptionInput).toHaveValue('New report description');
    });

    it('calls onCancel when back button is clicked', async () => {
      const onCancel = vi.fn();
      const { user } = renderComponent({ onCancel });

      await user.click(screen.getByRole('button', { name: /back/i }));

      expect(onCancel).toHaveBeenCalled();
    });

    it('calls onCancel when cancel button is clicked', async () => {
      const onCancel = vi.fn();
      const { user } = renderComponent({ onCancel });

      await user.click(screen.getByRole('button', { name: /cancel/i }));

      expect(onCancel).toHaveBeenCalled();
    });
  });

  describe('form submission', () => {
    it('creates new report successfully', async () => {
      const onView = vi.fn();
      server.use(
        http.post(getUrl('initiatives/test-initiative-id/report-documents'), async () =>
          ResponseSuccess({ ...mockReportDocument, title: 'New Report' }),
        ),
      );

      const { user } = renderComponent({ onView });

      await user.type(screen.getByPlaceholderText(/title/i), 'New Report');
      await user.click(screen.getByRole('button', { name: /create report/i }));

      await waitFor(() => {
        expect(onView).toHaveBeenCalledWith(mockReportDocument._id);
      });
    });

    it('updates existing report successfully', async () => {
      const onCancel = vi.fn();
      server.use(
        http.put(getUrl('initiatives/test-initiative-id/report-documents/test-report-id'), async () =>
          ResponseSuccess(mockReportDocument),
        ),
      );

      const { user } = renderComponent({ reportDocument: mockReportDocument, onCancel });

      const titleInput = screen.getByPlaceholderText(/title/i);
      await user.clear(titleInput);
      await user.type(titleInput, 'Updated Report');
      await user.click(screen.getByRole('button', { name: /save/i }));

      await waitFor(() => {
        expect(onCancel).toHaveBeenCalled();
      });
    });

    it('handles API error during creation', async () => {
      const { generateErrorToast } = await import('@g17eco/molecules/toasts');
      server.use(
        http.post(getUrl('initiatives/test-initiative-id/report-documents'), async () =>
          ResponseError({ message: 'Creation failed', status: 500 }),
        ),
      );

      const { user } = renderComponent();

      await user.type(screen.getByPlaceholderText(/title/i), 'New Report');
      await user.click(screen.getByRole('button', { name: /create report/i }));

      expect(generateErrorToast).toHaveBeenCalled();
    });

    it('shows loading state during submission', async () => {
      server.use(
        http.post(getUrl('initiatives/test-initiative-id/report-documents'), async () => {
          await new Promise((resolve) => setTimeout(resolve, 100));
          return ResponseSuccess(mockReportDocument);
        }),
      );

      const { user } = renderComponent();

      await user.type(screen.getByPlaceholderText(/title/i), 'New Report');
      const submitButton = screen.getByRole('button', { name: /create report/i });

      await user.click(submitButton);

      expect(submitButton).toBeDisabled();
    });
  });

  describe('template options', () => {
    it('displays all template options', async () => {
      const { user } = renderComponent();

      // Find the select component input
      const templateSelect = screen.getByRole('combobox');
      await user.click(templateSelect);

      expect(screen.getByText('Simple')).toBeInTheDocument();
      expect(screen.getByText('AI Generated')).toBeInTheDocument();
    });

    it('has blank template selected by default', () => {
      renderComponent();

      expect(screen.getByText('Blank')).toBeInTheDocument();
    });
  });

  describe('accessibility', () => {
    it('has proper form labels', async () => {
      renderComponent();

      expect(screen.getByText('Title *')).toBeInTheDocument();
      expect(screen.getByText('Description')).toBeInTheDocument();
      expect(screen.getByText('Template *')).toBeInTheDocument();

      // Survey field should be present after API loads
      expect(await screen.findByText('Survey')).toBeInTheDocument();
    });

    it('marks required fields appropriately', () => {
      renderComponent();

      const titleInput = screen.getByPlaceholderText(/title/i);
      expect(titleInput).toBeRequired();
    });
  });

  describe('surveys select functionality', () => {
    describe('rendering', () => {
      it('renders survey select field with default placeholder', async () => {
        renderComponent();

        expect(await screen.findByTestId('survey-select')).toBeInTheDocument();
        expect(screen.getByText(/select a survey \(optional\)/i)).toBeInTheDocument();
      });

      it('displays loading placeholder while surveys are loading', () => {
        // Override with slow response
        server.use(
          http.get(getUrl('initiatives/test-initiative-id/surveys'), async () => {
            await new Promise((resolve) => setTimeout(resolve, 100));
            return ResponseSuccess(mockSurveys);
          }),
        );

        renderComponent();

        expect(screen.getByText(/loading surveys\.\.\./i)).toBeInTheDocument();
      });

      it('displays survey options once loaded', async () => {
        renderComponent();

        // Wait for surveys to load and dropdown to be available
        expect(await screen.findByText(/select a survey \(optional\)/i)).toBeInTheDocument();

        // Open the dropdown to see options
        const surveySelect = screen.getByText(/select a survey \(optional\)/i);
        selectEvent.openMenu(surveySelect);

        // Check for survey options
        await waitFor(() => {
          expect(screen.getByText(/Environmental Survey 2024/)).toBeInTheDocument();
          expect(screen.getByText(/Social Impact Assessment/)).toBeInTheDocument();
          expect(screen.getByText(/Governance Review/)).toBeInTheDocument();
        });
      });

      it('shows completed status for completed surveys', async () => {
        renderComponent();

        expect(await screen.findByText(/select a survey \(optional\)/i)).toBeInTheDocument();

        const surveySelect = screen.getByText(/select a survey \(optional\)/i);
        selectEvent.openMenu(surveySelect);

        await waitFor(() => {
          expect(screen.getByText(/- Completed/)).toBeInTheDocument();
        });
      });

      it('shows combined indicator for aggregated surveys', async () => {
        renderComponent();

        expect(await screen.findByText(/select a survey \(optional\)/i)).toBeInTheDocument();

        const surveySelect = screen.getByText(/select a survey \(optional\)/i);
        selectEvent.openMenu(surveySelect);

        await waitFor(() => {
          expect(screen.getByText(/\(Combined\)/)).toBeInTheDocument();
        });
      });
    });

    describe('interactions', () => {
      it('allows selecting a survey from dropdown', async () => {
        renderComponent();

        expect(await screen.findByText(/select a survey \(optional\)/i)).toBeInTheDocument();

        const surveySelect = screen.getByText(/select a survey \(optional\)/i);
        await selectEvent.select(surveySelect, 'Environmental Survey 2024 2024 January - Completed');

        // Verify survey is selected
        await waitFor(() => {
          expect(screen.getByText('Environmental Survey 2024 2024 January - Completed')).toBeInTheDocument();
        });
      });

      it('includes selected survey in form submission', async () => {
        const onView = vi.fn();
        server.use(
          http.post(getUrl('initiatives/test-initiative-id/report-documents'), async ({ request }) => {
            const body = (await request.json()) as Record<string, any>;
            expect(body.surveyId).toBe('survey-1');
            return ResponseSuccess({ ...mockReportDocument, title: 'New Report' });
          }),
        );

        const { user } = renderComponent({ onView });

        // Fill out form
        await user.type(screen.getByPlaceholderText(/title/i), 'New Report');

        // Select survey
        expect(await screen.findByText(/select a survey \(optional\)/i)).toBeInTheDocument();

        const surveySelect = screen.getByText(/select a survey \(optional\)/i);
        await selectEvent.select(surveySelect, 'Environmental Survey 2024 2024 January - Completed');

        // Submit form
        await user.click(screen.getByRole('button', { name: /create report/i }));

        await waitFor(() => {
          expect(onView).toHaveBeenCalledWith(mockReportDocument._id);
        });
      });

      it('submits form without survey selection when none selected', async () => {
        const onView = vi.fn();
        server.use(
          http.post(getUrl('initiatives/test-initiative-id/report-documents'), async ({ request }) => {
            const body = (await request.json()) as Record<string, any>;
            expect(body.surveyId).toBeUndefined();
            return ResponseSuccess({ ...mockReportDocument, title: 'New Report' });
          }),
        );

        const { user } = renderComponent({ onView });

        // Fill out form without selecting survey
        await user.type(screen.getByPlaceholderText(/title/i), 'New Report');

        // Submit form
        await user.click(screen.getByRole('button', { name: /create report/i }));

        await waitFor(() => {
          expect(onView).toHaveBeenCalledWith(mockReportDocument._id);
        });
      });
    });

    describe('loading states', () => {
      it('disables survey select while surveys are loading', () => {
        server.use(
          http.get(getUrl('initiatives/test-initiative-id/surveys'), async () => {
            await new Promise((resolve) => setTimeout(resolve, 100));
            return ResponseSuccess(mockSurveys);
          }),
        );

        renderComponent();

        // Check loading state
        expect(screen.getByText(/loading surveys\.\.\./i)).toBeInTheDocument();
      });

      it('enables survey select after surveys load', async () => {
        renderComponent();

        expect(await screen.findByText(/select a survey \(optional\)/i)).toBeInTheDocument();
      });
    });

    describe('error handling', () => {
      it('handles survey loading error gracefully', async () => {
        server.use(
          http.get(getUrl('initiatives/test-initiative-id/surveys'), async () =>
            ResponseError({ message: 'Failed to load surveys', status: 500 }),
          ),
        );

        renderComponent();

        // Survey field should still be present with default placeholder
        expect(await screen.findByTestId('survey-select')).toBeInTheDocument();
        expect(await screen.findByText(/select a survey \(optional\)/i)).toBeInTheDocument();
      });

      it('continues to work when no surveys are available', async () => {
        server.use(http.get(getUrl('initiatives/test-initiative-id/surveys'), async () => ResponseSuccess([])));

        const { user } = renderComponent();

        expect(await screen.findByText(/select a survey \(optional\)/i)).toBeInTheDocument();

        // Should still be able to submit form without surveys
        await user.type(screen.getByPlaceholderText(/title/i), 'New Report');
        const submitButton = screen.getByRole('button', { name: /create report/i });
        expect(submitButton).toBeEnabled();
      });
    });

    describe('survey sorting', () => {
      it('displays surveys in order (sorted by effective date)', async () => {
        renderComponent();

        expect(await screen.findByText(/select a survey \(optional\)/i)).toBeInTheDocument();

        const surveySelect = screen.getByText(/select a survey \(optional\)/i);
        selectEvent.openMenu(surveySelect);

        // Just verify all surveys appear (sorting is tested at the component level)
        await waitFor(() => {
          expect(screen.getByText(/Governance Review/)).toBeInTheDocument();
          expect(screen.getByText(/Environmental Survey 2024/)).toBeInTheDocument();
          expect(screen.getByText(/Social Impact Assessment/)).toBeInTheDocument();
        });
      });
    });
  });
});
