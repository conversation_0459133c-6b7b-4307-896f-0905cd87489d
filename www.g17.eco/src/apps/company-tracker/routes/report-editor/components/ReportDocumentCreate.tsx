/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { useState } from 'react';
import { generateErrorToast, generateToast } from '@g17eco/molecules/toasts';
import { Button } from 'reactstrap';
import { DashboardRow, DashboardSection } from '@g17eco/molecules/dashboard';
import { CreateReportDocumentMin, ReportDocument, ReportDocumentTemplate } from '@g17eco/types/reportDocument';
import {
  useCreateReportDocumentMutation,
  useUpdateReportDocumentMutation,
} from '@api/initiative-report-documents';
import { getGroup } from '@g17eco/core';
import { FieldProps, FormGenerator } from '@g17eco/molecules/form';
import { SubmitButton } from '@g17eco/molecules/button/SubmitButton';
import { getSurveyName } from '@utils/surveyData';
import { isAggregatedSurvey } from '@utils/survey';
import { sortEffectiveDateDesc } from '@utils/sort';
import { useGetSurveysForInitiativeQuery } from '@api/surveys';

interface Props {
  initiativeId: string;
  type: string;
  onCancel: () => void;
  onView: (reportId: string) => void;
  reportDocument?: ReportDocument;
}

const getInitialForm = ({ initiativeId, type }: { initiativeId: string; type: string }): CreateReportDocumentMin => ({
  title: '',
  description: '',
  initiativeId,
  type,
  config: {
    template: ReportDocumentTemplate.Blank,
  },
});

type FormState = CreateReportDocumentMin | ReportDocument;

export const ReportDocumentCreate = (props: Props) => {
  const { initiativeId, type, onCancel, onView, reportDocument } = props;

  const [form, setForm] = useState<FormState>(reportDocument ?? getInitialForm({ initiativeId, type }));

  const [createCustomReport, { isLoading: isCreating }] = useCreateReportDocumentMutation();
  const [updateCustomReport, { isLoading: isUpdating }] = useUpdateReportDocumentMutation();
  const { data: surveys = [], isLoading: isLoadingSurveys } = useGetSurveysForInitiativeQuery(initiativeId);

  const isDisabled = !form.title || !form.type || isCreating || isUpdating;
  const reportId = form._id;

  const surveyOptions = [...surveys].sort(sortEffectiveDateDesc).map((survey) => ({
    value: survey._id,
    label: `${getSurveyName(survey)}${isAggregatedSurvey(survey.type) ? ' (Combined)' : ''}${survey.completedDate ? ' - Completed' : ''}`,
  }));

  const fields = [
  {
    code: 'title',
    label: 'Title',
    type: 'text',
    required: true,
  },
  {
    code: 'description',
    label: 'Description',
    type: 'textarea',
  },
  {
    code: 'surveyId',
    label: 'Survey',
    type: 'select',
    required: false,
    options: surveyOptions,
    placeholder: isLoadingSurveys ? 'Loading surveys...' : 'Select a survey (optional)',
    disabled: isLoadingSurveys,
    testId: 'survey-select',
  },
  {
    parentCode: 'config',
    code: 'template',
    label: 'Template',
    type: 'select',
    required: true,
    options: [
      { value: ReportDocumentTemplate.Blank, label: 'Blank' },
      { value: ReportDocumentTemplate.Simple, label: 'Simple' },
      { value: ReportDocumentTemplate.AiGenerated, label: 'AI Generated' },
    ],
    disabled: Boolean(reportId),
    testId: 'template-type-select',
  },
] satisfies FieldProps<CreateReportDocumentMin>[];

  const callbackSubmit = (reportDocument: ReportDocument) => {
    if (reportId) {
      onCancel();
      return;
    }
    setForm(reportDocument);
    onView(reportDocument._id);
  };

  const handleSubmit = async () => {
    if (isDisabled) {
      return;
    }

    const action = reportId ? 'updated' : 'created';
    const method = reportId && 'created' in form ? updateCustomReport(form) : createCustomReport(form);

    return method
      .unwrap()
      .then((reportDocument) => {
        generateToast({
          title: `Custom report ${action}`,
          color: 'success',
          message: `Custom report template has been ${action}`,
        });
        callbackSubmit(reportDocument);
      })
      .catch((error) => {
        generateErrorToast(error);
      });
  };

  const updateForm = (
    update: React.ChangeEvent<{
      name: string;
      value: string | number | undefined;
    }>,
  ) => {
    const { name, value } = update.target;

    const field = fields.find((field) => field.code === name);
    if (!field) {
      return;
    }

    if (!field.parentCode) {
      setForm((currentForm) => ({
        ...currentForm,
        [name]: value,
      }));
      return;
    }

    setForm((currentForm) => {
      const objectField = currentForm[field.parentCode as keyof FormState] as Record<string, any>;
      return {
        ...currentForm,
        [field.parentCode]: {
          ...objectField,
          [name]: value,
        },
      };
    });
  };

  const buttonText = reportId ? 'Save' : 'Create report';
  const group = getGroup('standards', type);
  return (
    <>
      <DashboardRow>
        <Button type='button' color='link' onClick={onCancel}>
          <i className='fas fa-arrow-left mr-2'></i>
          Back
        </Button>
      </DashboardRow>
      <DashboardRow>
        <div className='w-100 d-flex justify-content-between align-items-center'>
          <h3>{group?.name ?? type} Report</h3>
        </div>
      </DashboardRow>
      <DashboardSection paddingInternal={0}>
        <FormGenerator
          fields={fields}
          form={form}
          updateForm={updateForm}
        />
        <div className='d-flex flex-row justify-content-between'>
          <div className='mt-3 d-flex justify-content-end gap-3'>
            <Button color='link-secondary' onClick={onCancel}>
              Cancel
            </Button>
            <SubmitButton color='primary' onClick={handleSubmit} disabled={isDisabled}>
              {buttonText}
            </SubmitButton>
          </div>
        </div>
      </DashboardSection>
    </>
  );
};
