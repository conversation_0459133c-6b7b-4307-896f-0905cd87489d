import { useEffect, useMemo, useState } from 'react';
import { InitiativeQuestionManagementResponse, ViewType, useGetInitiativeQuestionsQuery } from '@api/admin-dashboard';
import { CheckboxState } from '@g17eco/atoms/checkbox';
import { ActionsToolbar } from '@features/survey-question-list/partials/ActionsToolbar';
import { BulkActionProps } from '@features/survey-question-list/survey-question-list';
import { OverridesOption, QuestionFilter, QuestionFilters } from './QuestionFilter';
import { QuestionTable, QuestionTableColumn } from './QuestionTable';
import { useAppSelector } from '@reducers/index';
import { getRootOrg, isRootOrg } from '@selectors/initiative';
import { BasicAlert } from '@g17eco/molecules/alert';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { useGetCustomTagsQuery } from '@api/metric-groups';
import { Loader } from '@g17eco/atoms/loader';
import { skipToken } from '@reduxjs/toolkit/query';
import { QUESTION } from '@constants/terminology';
import { checkCanAccessRootOrg } from '@selectors/user';
import { useSearchDebounceWithFlexMap } from '@hooks/useSearchDebounceWithFlexMap';
import { getFilteredUtrIdsByTag, isMatchSelectedOverrides, isMatchSelectedTags } from './util';
import { QuestionConfigurationButton, QuestionConfigurationModal } from '@features/question-configuration';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import QuestionManagementContainer from './QuestionManagementContainer';
import { useGetOrganizationMetricGroupsQuery } from '@api/organization';
import './QuestionManagement.scss';
import { isMatchSelectedPacks } from '@utils/metricGroup';
import { ConfigurationType, QuestionConfigurationButtonProps } from '@features/question-configuration/types';
import { BulkActionUtr } from '@g17eco/types/survey-question-list';

export enum ShowAs {
  Page = 'page',
  Section = 'section',
}

export const NO_QUESTIONS_MESSAGE = {
  FILTER: `No ${QUESTION.PLURAL} match your criteria`,
  IN_SCOPE: `No ${QUESTION.PLURAL} are used in scope`,
};

const initialState = {
  packs: [],
  tags: [],
  overrides: [],
};

type ActionsToolbarButton = {
  name: string;
  Component: typeof QuestionConfigurationButton;
  buttonProps: QuestionConfigurationButtonProps;
}

interface QuestionManagementProps {
  pageSize?: number;
  showAs?: ShowAs;
  initiativeId: string;
}

export const QuestionManagement = ({ pageSize, showAs = ShowAs.Section, initiativeId }: QuestionManagementProps) => {
  const isRootOrganization = useAppSelector(isRootOrg);
  const rootOrg = useAppSelector(getRootOrg);
  const canAccessRootOrg = useAppSelector(checkCanAccessRootOrg);
  const [viewType, setViewType] = useState<ViewType>(ViewType.InScope);
  const [filters, setFilters] = useState<QuestionFilters>(initialState);

  const [selectedQuestions, setSelectedQuestions] = useState<BulkActionUtr[]>([]);
  const [configType, setConfigType] = useState<ConfigurationType | undefined>();

  const {
    data = {} as InitiativeQuestionManagementResponse,
    isFetching,
    isLoading,
    isSuccess,
    refetch,
    error: loadUtrError,
  } = useGetInitiativeQuestionsQuery({ initiativeId, viewType }, { skip: !initiativeId });

  const { data: tags = [], error: loadTagError } = useGetCustomTagsQuery(rootOrg?._id ?? skipToken, {
    skip: !canAccessRootOrg,
  });

  const { data: metricGroups, error: loadMetricGroupError } = useGetOrganizationMetricGroupsQuery(initiativeId);

  const rootInitiativeUtrMap = useMemo(
    () =>
      new Map<string, InitiativeUniversalTracker>(
        data.rootInitiativeUtrs?.map((utr: InitiativeUniversalTracker) => [utr.universalTrackerId, utr])
      ),
    [data.rootInitiativeUtrs]
  );

  const utrIds = useMemo(() => {
    return (data.utrs || []).map((utr) => utr._id);
  }, [data.utrs]);

  const { onSearch, matchedQuestions } = useSearchDebounceWithFlexMap(data.utrs);

  const isFiltering = filters.packs.length > 0 || filters.tags.length > 0 || filters.overrides.length > 0;

  const filteredQuestions = useMemo(() => {
    if (isFiltering) {
      const filteredUtrIdsByTag = getFilteredUtrIdsByTag({ utrIds, tags, values: filters.tags });
      return matchedQuestions.filter(
        (utr) =>
          isMatchSelectedTags({ utrId: utr._id, selectedTags: filters.tags, filteredUtrIdsByTag }) &&
          isMatchSelectedPacks({ utr, packs: filters.packs, metricGroups: metricGroups ?? [] }) &&
          isMatchSelectedOverrides({ utr, rootInitiativeUtrMap, overrides: filters.overrides })
      );
    }
    return matchedQuestions;
  }, [isFiltering, matchedQuestions, utrIds, tags, filters, metricGroups, rootInitiativeUtrMap]);

  useEffect(() => {
    if (selectedQuestions.length === 0) {
      return;
    }
    const filteredUtrIds = filteredQuestions.map((question) => question._id);
    setSelectedQuestions((prev) => prev.filter((question) => filteredUtrIds.includes(question._id)));
  }, [filteredQuestions, selectedQuestions.length]);

  if (!rootOrg) {
    return <Loader />;
  }

  const isSelectAll = selectedQuestions.length > 0 && selectedQuestions.length === filteredQuestions?.length;

  const handleUnselectAll = () => setSelectedQuestions([]);

  const toggleSelectAll = () => (isSelectAll ? handleUnselectAll() : setSelectedQuestions(filteredQuestions));

  const toggleSelectQuestion = (utr: BulkActionUtr, status: CheckboxState) => {
    const newSelectedQuestions =
      status === CheckboxState.Checked
        ? selectedQuestions.filter((q) => q._id !== utr._id)
        : [...selectedQuestions, utr];

    return setSelectedQuestions(newSelectedQuestions);
  };

  const configureSelectedQuestion = ({ utr, type }: { utr: BulkActionUtr; type: ConfigurationType }) => {
    setSelectedQuestions([utr]);
    setConfigType(type);
  };

  const handleQuestionFilter = ({ name, value }: { name: string; value: string[] | OverridesOption[] }) => {
    setFilters((prev) => ({ ...prev, [name]: value }));
  };

  const handleReload = async ({ reloadSurvey = false }: { reloadSurvey?: boolean } = {}) => {
    if (reloadSurvey) {
      await refetch();
    }
  };

  const modalProps = {
    selectedQuestions,
    initiativeId,
    handleReload,
  };

  const renderActionToolbar = (
    props: Omit<BulkActionProps, 'surveyId' | 'triggerBulkAction' | 'selectedQuestions'> & {
      selectedQuestions: BulkActionUtr[];
    }
  ) => {
    const listButtons: ActionsToolbarButton[] = isRootOrganization
      ? [
          {
            name: 'metric-overrides',
            Component: QuestionConfigurationButton,
            buttonProps: {
              configType: ConfigurationType.MetricOverrides,
              ...modalProps,
            },
          },
          {
            name: 'input-overrides',
            Component: QuestionConfigurationButton,
            buttonProps: {
              configType: ConfigurationType.InputOverrides,
              ...modalProps,
            },
          },
          {
            name: 'tags',
            Component: QuestionConfigurationButton,
            buttonProps: {
              configType: ConfigurationType.Tags,
              ...modalProps,
            },
          },
        ]
      : [];
    return <ActionsToolbar {...props} buttons={listButtons} />;
  };

  const renderNoQuestionsAlert = () => {
    if (isFiltering) {
      return <BasicAlert type='secondary'>{NO_QUESTIONS_MESSAGE.FILTER}</BasicAlert>;
    }
    if (viewType === ViewType.InScope) {
      return <BasicAlert type='secondary'>{NO_QUESTIONS_MESSAGE.IN_SCOPE}</BasicAlert>;
    }
    return null;
  };

  const renderQuestionConfigurationModal = () => {
    if (!configType || selectedQuestions.length === 0) {
      return null;
    }

    return (
      <QuestionConfigurationModal
        isOpen={true}
        toggleOpen={() => {
          setSelectedQuestions([]);
          setConfigType(undefined);
        }}
        configType={configType}
        {...modalProps}
      />
    );
  };

  return (
    <QuestionManagementContainer>
      <QuestionFilter
        viewType={viewType}
        toggleViewType={(viewType: ViewType) => setViewType(viewType)}
        showFilters={showAs === ShowAs.Page}
        filters={filters}
        handleQuestionFilter={handleQuestionFilter}
        onSearch={onSearch}
        rootInitiativeId={rootOrg._id}
        canAccessRootOrg={canAccessRootOrg}
      />
      {[loadUtrError, loadTagError, loadMetricGroupError].map((error, idx) =>
        error ? (
          <BasicAlert key={idx} type='danger'>
            {error.message}
          </BasicAlert>
        ) : null
      )}
      <LoadingPlaceholder height={200} isLoading={selectedQuestions.length === 1 ? isFetching : isLoading}>
        {isSuccess && filteredQuestions.length > 0 ? (
          <QuestionTable
            data={filteredQuestions}
            utrTagMap={data.utrTagMap}
            rootInitiativeUtrMap={rootInitiativeUtrMap}
            toggleSelectAll={toggleSelectAll}
            selectedQuestions={selectedQuestions}
            toggleSelectQuestion={toggleSelectQuestion}
            isSelectAll={isSelectAll}
            pageSize={pageSize}
            isRootOrganization={isRootOrganization}
            hiddenColumns={showAs === ShowAs.Page ? [] : [QuestionTableColumn.SingleAction]}
            configureSelectedQuestion={configureSelectedQuestion}
          />
        ) : (
          renderNoQuestionsAlert()
        )}
        <div className='question--list__sticky-toolbar'>
          {renderActionToolbar({
            initiativeId,
            questionCount: filteredQuestions.length,
            selectedQuestions,
            toggleSelectAll,
            handleClose: handleUnselectAll,
          })}
        </div>
      </LoadingPlaceholder>
      {renderQuestionConfigurationModal()}
    </QuestionManagementContainer>
  );
};
