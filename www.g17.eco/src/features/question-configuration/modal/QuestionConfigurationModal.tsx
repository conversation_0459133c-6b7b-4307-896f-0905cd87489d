import { TagContainer } from '@features/tags/TagContainer';
import { InputOverrideContainer } from '@features/question-configuration';
import { Modal, ModalBody, ModalHeader } from 'reactstrap';
import { getModalTitle } from '../utils';
import { ConfigurationType, QuestionConfigurationModalProps } from '../types';
import { QUESTION, SURVEY } from '@constants/terminology';
import { MetricOverrideContainer } from '../metric-override/MetricOverrideContainer';
import { BulkActionUtr } from '@g17eco/types/survey-question-list';
import { getNumericSelectedQuestions } from '@features/survey-question-list/utils';

const MULTI_METRIC_SELECTED_DESCRIPTION = `You have selected multiple ${QUESTION.PLURAL}. Changing the config will impact all ${QUESTION.PLURAL} selected.`;

export const getDescription = ({
  configType,
  isMultipleQuestionSelected,
}: {
  configType: ConfigurationType | undefined;
  isMultipleQuestionSelected: boolean;
}) => {
  switch (configType) {
    case ConfigurationType.MetricOverrides: {
      const description = `${QUESTION.CAPITALIZED_SINGULAR} overrides are set globally. All ${SURVEY.PLURAL} created in the future will respect these rules.`;
      if (!isMultipleQuestionSelected) {
        return description;
      }
      return (
        <div>
          {description}
          <div className='mt-2'>{MULTI_METRIC_SELECTED_DESCRIPTION}</div>
        </div>
      );
    }
    case ConfigurationType.InputOverrides: {
      const description =
        `Input overrides apply globally, affecting new ${SURVEY.PLURAL} and unanswered ${QUESTION.PLURAL}. ` +
        `When exporting or downloading reports, selecting '${QUESTION.SINGULAR} overrides'` +
        'ensures that user-entered figures are automatically converted to the following settings.';
      if (!isMultipleQuestionSelected) {
        return <div className='text-ThemeTextMedium'>{description}</div>;
      }
      return (
        <div className='text-ThemeTextMedium'>
          <div className='my-1 fw-bold'>{MULTI_METRIC_SELECTED_DESCRIPTION}</div>
          {description}
        </div>
      );
    }
    case ConfigurationType.Tags:
      return (
        `Tags are set globally. Any ${SURVEY.PLURAL} created in the future  will be tagged with the below. ` +
        `${QUESTION.CAPITALIZED_PLURAL} already added to scope will not update their tags`
      );
    default:
      return '';
  }
};

type ConfigurationFormProps = Omit<QuestionConfigurationModalProps, 'handleReload' | 'isOpen'> & {
  numericSelectedQuestions: BulkActionUtr[];
  handleReload: (props?: { reloadSurvey?: boolean; closeModal?: boolean }) => Promise<void>;
};

const ConfigurationForm = ({ configType, toggleOpen, ...configFormProps }: ConfigurationFormProps) => {
  switch (configType) {
    case ConfigurationType.MetricOverrides:
      return (
        <MetricOverrideContainer
          key={ConfigurationType.MetricOverrides}
          {...configFormProps}
          handleCloseModal={toggleOpen}
        />
      );
    case ConfigurationType.InputOverrides:
      return (
        <InputOverrideContainer
          key={ConfigurationType.InputOverrides}
          {...configFormProps}
          handleCloseModal={toggleOpen}
        />
      );
    case ConfigurationType.Tags:
      return <TagContainer key={ConfigurationType.Tags} {...configFormProps} />;
    default:
      return null;
  }
};

export const QuestionConfigurationModal = (props: QuestionConfigurationModalProps) => {
  const { isOpen, toggleOpen, configType, initiativeId, selectedQuestions, handleReload: onReload } = props;

  if (!selectedQuestions.length) {
    return null;
  }

  const handleReload: ConfigurationFormProps['handleReload'] = async (props) => {
    if (props?.closeModal) {
      toggleOpen();
    }
    onReload(props);
  };

  const configFormProps = {
    configType,
    toggleOpen,
    initiativeId,
    selectedQuestions,
    numericSelectedQuestions: getNumericSelectedQuestions(selectedQuestions),
    handleReload,
  };

  return (
    <Modal isOpen={isOpen} toggle={toggleOpen} backdrop='static'>
      <ModalHeader toggle={toggleOpen}>{getModalTitle(selectedQuestions)}</ModalHeader>
      <ModalBody className='pb-0'>
        {getDescription({ configType, isMultipleQuestionSelected: selectedQuestions.length > 1 })}
        <div className='divider mb-1 mt-3' />
      </ModalBody>
      <ConfigurationForm {...configFormProps} />
    </Modal>
  );
};
