import { describe } from 'vitest';
import {
  getAggregationOptionsByValueType,
  getLabelByCode,
  getOptionsByCode,
  getInitialUtrvConfig,
  getInitialAggregationConfig,
  hasDataChanged,
} from './utils';
import { UtrValueType, ValueAggregation, AggregationMode, AggregationConfig } from '@g17eco/types/universalTracker';
import { UtrvConfigValue } from './constants';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import { BulkActionUtr } from '@g17eco/types/survey-question-list';

describe('getLabelByCode', () => {
  it('should return correct labels for all config codes', () => {
    expect(getLabelByCode('verificationRequired')).toBe('Verification');
    expect(getLabelByCode('evidenceRequired')).toBe('Evidence');
    expect(getLabelByCode('noteRequired')).toBe('Further explanation/notes');
    expect(getLabelByCode('isPrivate')).toBe('Metric privacy');
    expect(getLabelByCode('aggregationConfig')).toBe('Aggregation settings');
  });

  it('should return empty string for unknown codes', () => {
    expect(getLabelByCode('unknown' as any)).toBe('');
  });
});

describe('getOptionsByCode', () => {
  it('should return correct options for verificationRequired', () => {
    const options = getOptionsByCode('verificationRequired');

    expect(options).toHaveLength(3);
    expect(options[0]).toEqual({
      label: 'Use report default settings',
      value: UtrvConfigValue.Default,
    });
    expect(options[1]).toEqual({
      label: 'Verification always optional',
      value: UtrvConfigValue.Optional,
    });
    expect(options[2]).toEqual({
      label: 'Verification always required',
      value: UtrvConfigValue.Required,
    });
  });

  it('should return correct options for isPrivate', () => {
    const options = getOptionsByCode('isPrivate');

    expect(options).toHaveLength(3);
    expect(options[1]).toEqual({
      label: 'Metrics always public',
      value: UtrvConfigValue.Optional,
    });
    expect(options[2]).toEqual({
      label: 'Metrics always private',
      value: UtrvConfigValue.Required,
    });
  });


});

describe('getAggregationOptionsByValueType', () => {
  it('should always include default option first', () => {
    const options = getAggregationOptionsByValueType(UtrValueType.Number, AggregationMode.Combined);

    expect(options[0]).toEqual({
      label: 'Use default settings (what they are)',
      value: 'default',
    });
  });

  it('should return correct options for Number type in Combined mode', () => {
    const options = getAggregationOptionsByValueType(UtrValueType.Number, AggregationMode.Combined);

    // Should include default option plus compatible aggregations
    expect(options.length).toBeGreaterThan(1);
    expect(options[0].value).toBe('default');

    // Check for specific aggregation types (after default)
    const aggregationValues = options.slice(1).map((opt) => opt.value);
    expect(aggregationValues).toEqual([
      ValueAggregation.LatestAggregator,
      ValueAggregation.EmptyAggregator,
      ValueAggregation.ValueCountAggregator,
      ValueAggregation.ValueSumAggregator,
      ValueAggregation.ValueAverageAggregator,
    ]);
  });

  it('should return correct options for Number type in Children mode', () => {
    const options = getAggregationOptionsByValueType(UtrValueType.Number, AggregationMode.Children);

    // Should include default option plus compatible aggregations for children mode
    expect(options.length).toBeGreaterThan(0);
    expect(options[0].value).toBe('default');

    // Children mode should have fewer options than Combined mode
    expect(options.length).toBeLessThan(
      getAggregationOptionsByValueType(UtrValueType.Number, AggregationMode.Combined).length,
    );
  });

  it('should return correct options for Text type', () => {
    const options = getAggregationOptionsByValueType(UtrValueType.Text, AggregationMode.Combined);

    expect(options.length).toBeGreaterThan(1);
    expect(options[0].value).toBe('default');

    // Check that text-compatible aggregations are included
    const aggregationValues = options.slice(1).map((opt) => opt.value);
    expect(aggregationValues).toEqual([
      ValueAggregation.LatestAggregator,
      ValueAggregation.EmptyAggregator,
      ValueAggregation.ValueCountAggregator,
      ValueAggregation.TextCountAggregator,
      ValueAggregation.ValueConcatenateAggregator,
    ]);
  });

  it('should return correct options for Table type', () => {
    const options = getAggregationOptionsByValueType(UtrValueType.Table, AggregationMode.Combined);

    expect(options.length).toBeGreaterThan(1);
    expect(options[0].value).toBe('default');

    // Check that table-compatible aggregations are included
    const aggregationValues = options.slice(1).map((opt) => opt.value);
    expect(aggregationValues).toEqual([
      ValueAggregation.LatestAggregator,
      ValueAggregation.EmptyAggregator,
      ValueAggregation.ValueCountAggregator,
      ValueAggregation.TableColumnAggregator,
      ValueAggregation.TableConcatenationAggregator,
      ValueAggregation.TableRowGroupAggregator,
    ]);
  });

  it('should return unique options without duplicates', () => {
    const options = getAggregationOptionsByValueType(UtrValueType.Number, AggregationMode.Combined);
    const values = options.map((opt) => opt.value);
    const uniqueValues = Array.from(new Set(values));

    expect(values.length).toBe(uniqueValues.length);
  });

  it('should return only default option for unknown valueType', () => {
    const options = getAggregationOptionsByValueType('unknown' as any, AggregationMode.Combined);
    expect(options).toEqual([
      {
        label: 'Use default settings (what they are)',
        value: 'default',
      },
    ]);
  });
});

describe('getInitialUtrvConfig', () => {
  it('should return default config when no initiative UTRs exist', () => {
    const result = getInitialUtrvConfig({
      initiativeUtrMap: new Map(),
      selectedQuestions: [{ _id: '1' }] as BulkActionUtr[],
    });

    expect(result).toEqual({
      verificationRequired: UtrvConfigValue.Default,
      evidenceRequired: UtrvConfigValue.Default,
      noteRequired: UtrvConfigValue.Default,
      isPrivate: UtrvConfigValue.Default,
    });
  });

  it('should merge config from initiative UTRs', () => {
    const initiativeUtr: InitiativeUniversalTracker = {
      _id: '1',
      initiativeId: 'init1',
      universalTrackerId: 'utr1',
      utrvConfig: {
        verificationRequired: UtrvConfigValue.Required,
        evidenceRequired: UtrvConfigValue.Optional,
        noteRequired: UtrvConfigValue.Default,
        isPrivate: UtrvConfigValue.Default,
      },
    } as InitiativeUniversalTracker;

    const initiativeUtrMap = new Map([['1', initiativeUtr]]);
    const selectedQuestions = [{ _id: '1' }] as BulkActionUtr[];

    const result = getInitialUtrvConfig({
      initiativeUtrMap,
      selectedQuestions,
    });

    expect(result).toEqual({
      verificationRequired: UtrvConfigValue.Required,
      evidenceRequired: UtrvConfigValue.Optional,
      noteRequired: UtrvConfigValue.Default,
      isPrivate: UtrvConfigValue.Default,
    });
  });

  it('should prioritize non-default values when merging multiple questions', () => {
    const initiativeUtr1: InitiativeUniversalTracker = {
      _id: '1',
      initiativeId: 'init1',
      universalTrackerId: 'utr1',
      utrvConfig: {
        verificationRequired: UtrvConfigValue.Default,
        evidenceRequired: UtrvConfigValue.Required,
        noteRequired: UtrvConfigValue.Default,
        isPrivate: UtrvConfigValue.Default,
      },
    } as InitiativeUniversalTracker;

    const initiativeUtr2: InitiativeUniversalTracker = {
      _id: '2',
      initiativeId: 'init1',
      universalTrackerId: 'utr2',
      utrvConfig: {
        verificationRequired: UtrvConfigValue.Optional,
        evidenceRequired: UtrvConfigValue.Default,
        noteRequired: UtrvConfigValue.Required,
        isPrivate: UtrvConfigValue.Default,
      },
    } as InitiativeUniversalTracker;

    const initiativeUtrMap = new Map([
      ['1', initiativeUtr1],
      ['2', initiativeUtr2],
    ]);
    const selectedQuestions = [{ _id: '1' }, { _id: '2' }] as BulkActionUtr[];

    const result = getInitialUtrvConfig({
      initiativeUtrMap,
      selectedQuestions,
    });

    expect(result).toEqual({
      verificationRequired: UtrvConfigValue.Optional,
      evidenceRequired: UtrvConfigValue.Required,
      noteRequired: UtrvConfigValue.Required,
      isPrivate: UtrvConfigValue.Default,
    });
  });
});

describe('getInitialAggregationConfig', () => {
  it('should return default config when no initiative UTRs exist', () => {
    const result = getInitialAggregationConfig({
      initiativeUtrMap: new Map(),
      utrId: '1',
    });

    expect(result).toEqual({
      modes: {
        children: { valueAggregation: 'default' },
        combined: { valueAggregation: 'default' },
      },
    });
  });

  it('should return aggregation config from initiative UTR', () => {
    const aggregationConfig: AggregationConfig = {
      modes: {
        children: { valueAggregation: ValueAggregation.ValueSumAggregator },
        combined: { valueAggregation: ValueAggregation.ValueAverageAggregator },
      },
    };

    const initiativeUtr: InitiativeUniversalTracker = {
      _id: '1',
      initiativeId: 'init1',
      universalTrackerId: 'utr1',
      aggregationConfig,
    } as InitiativeUniversalTracker;

    const initiativeUtrMap = new Map([['1', initiativeUtr]]);

    const result = getInitialAggregationConfig({
      initiativeUtrMap,
      utrId: '1',
    });

    expect(result).toEqual(aggregationConfig);
  });

  it('should return default config when UTR not found', () => {
    const initiativeUtrMap = new Map();

    const result = getInitialAggregationConfig({
      initiativeUtrMap,
      utrId: 'nonexistent',
    });

    expect(result).toEqual({
      modes: {
        children: { valueAggregation: 'default' },
        combined: { valueAggregation: 'default' },
      },
    });
  });
});

describe('hasDataChanged', () => {
  const initialUtrvConfig = {
    verificationRequired: UtrvConfigValue.Default,
    evidenceRequired: UtrvConfigValue.Default,
    noteRequired: UtrvConfigValue.Default,
    isPrivate: UtrvConfigValue.Default,
  };

  const initialAggregationConfig: AggregationConfig = {
    modes: {
      children: { valueAggregation: 'default' },
      combined: { valueAggregation: 'default' },
    },
  };

  it('should return false when no data has changed', () => {
    const result = hasDataChanged({
      initialUtrvConfig,
      currentUtrvConfig: { ...initialUtrvConfig },
      initialAggregationConfig,
      currentAggregationConfig: { ...initialAggregationConfig },
    });

    expect(result).toBe(false);
  });

  it('should return true when UTRV config has changed', () => {
    const currentUtrvConfig = {
      ...initialUtrvConfig,
      verificationRequired: UtrvConfigValue.Required,
    };

    const result = hasDataChanged({
      initialUtrvConfig,
      currentUtrvConfig,
      initialAggregationConfig,
      currentAggregationConfig: initialAggregationConfig,
    });

    expect(result).toBe(true);
  });

  it('should return true when aggregation config has changed', () => {
    const currentAggregationConfig: AggregationConfig = {
      modes: {
        children: { valueAggregation: ValueAggregation.ValueSumAggregator },
        combined: { valueAggregation: 'default' },
      },
    };

    const result = hasDataChanged({
      initialUtrvConfig,
      currentUtrvConfig: initialUtrvConfig,
      initialAggregationConfig,
      currentAggregationConfig,
    });

    expect(result).toBe(true);
  });

  it('should handle undefined aggregation configs', () => {
    const result = hasDataChanged({
      initialUtrvConfig,
      currentUtrvConfig: initialUtrvConfig,
      initialAggregationConfig: undefined,
      currentAggregationConfig: undefined,
    });

    expect(result).toBe(false);
  });

  it('should return true when one aggregation config is undefined and other is not', () => {
    const result = hasDataChanged({
      initialUtrvConfig,
      currentUtrvConfig: initialUtrvConfig,
      initialAggregationConfig: undefined,
      currentAggregationConfig: initialAggregationConfig,
    });

    expect(result).toBe(true);
  });
});
