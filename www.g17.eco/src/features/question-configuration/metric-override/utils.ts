import { BulkActionUtr } from '@g17eco/types/survey-question-list';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import { OverridesConfigCode, UTRV_CONFIG_CODES, UtrvConfigCode, UtrvConfigType, UtrvConfigValue } from './constants';
import { QUESTION } from '@constants/terminology';
import {
  ValueAggregation,
  AggregationMode,
  AggregationConfig,
  UtrValueType,
} from '@g17eco/types/universalTracker';
import { aggregationLabels, getCompatibleAggregations } from '@utils/universalTracker';

const defaultUtrvConfig: UtrvConfigType = {
  verificationRequired: UtrvConfigValue.Default,
  evidenceRequired: UtrvConfigValue.Default,
  noteRequired: UtrvConfigValue.Default,
  isPrivate: UtrvConfigValue.Default,
};

export const getInitialUtrvConfig = ({
  initiativeUtrMap,
  selectedQuestions,
}: {
  initiativeUtrMap: Map<string, InitiativeUniversalTracker>;
  selectedQuestions: BulkActionUtr[];
}) => {
  if (initiativeUtrMap.size === 0) {
    return defaultUtrvConfig;
  }

  return selectedQuestions.reduce(
    (acc, question) => {
      const initiativeUtr = initiativeUtrMap.get(question._id);
      if (!initiativeUtr) {
        return acc;
      }

      UTRV_CONFIG_CODES.forEach((code) => {
        if (acc[code] !== UtrvConfigValue.Default) {
          return;
        }
        if (initiativeUtr.utrvConfig?.[code]) {
          acc[code] = initiativeUtr.utrvConfig[code];
        }
      });
      return acc;
    },
    { ...defaultUtrvConfig },
  );
};

export const getLabelByCode = (code: OverridesConfigCode) => {
  switch (code) {
    case 'verificationRequired':
      return 'Verification';
    case 'evidenceRequired':
      return 'Evidence';
    case 'noteRequired':
      return 'Further explanation/notes';
    case 'isPrivate':
      return `${QUESTION.CAPITALIZED_SINGULAR} privacy`;
    case 'aggregationConfig':
      return 'Aggregation settings';
    default:
      return '';
  }
};

export const getOptionsByCode = (code: UtrvConfigCode) => {
  return [
    {
      label: 'Use report default settings',
      value: UtrvConfigValue.Default,
    },
    {
      label:
        code === 'isPrivate'
          ? `${QUESTION.CAPITALIZED_PLURAL} always public`
          : `${getLabelByCode(code)} always optional`,
      value: UtrvConfigValue.Optional,
    },
    {
      label:
        code === 'isPrivate'
          ? `${QUESTION.CAPITALIZED_PLURAL} always private`
          : `${getLabelByCode(code)} always required`,
      value: UtrvConfigValue.Required,
    },
  ];
};

export const getInitialAggregationConfig = ({
  initiativeUtrMap,
  utrId,
}: {
  initiativeUtrMap: Map<string, InitiativeUniversalTracker>;
  utrId: string | undefined;
}) => {
  const defaultConfig: AggregationConfig = {
    modes: {
      children: {
        valueAggregation: 'default',
      },
      combined: {
        valueAggregation: 'default',
      },
    },
  };

  if (!utrId || initiativeUtrMap.size === 0) {
    return defaultConfig;
  }

  const aggregationConfig = initiativeUtrMap.get(utrId)?.aggregationConfig;

  return aggregationConfig ?? defaultConfig;
};

export const hasDataChanged = (params: {
  initialUtrvConfig: UtrvConfigType;
  currentUtrvConfig: UtrvConfigType;
  initialAggregationConfig: AggregationConfig | undefined;
  currentAggregationConfig: AggregationConfig | undefined;
}) => {
  const { initialUtrvConfig, currentUtrvConfig, initialAggregationConfig, currentAggregationConfig } = params;
  const aggregationChanged = JSON.stringify(initialAggregationConfig) !== JSON.stringify(currentAggregationConfig);
  const utrvConfigChanged = Object.keys(initialUtrvConfig).some(
    (code) => initialUtrvConfig[code as UtrvConfigCode] !== currentUtrvConfig[code as UtrvConfigCode],
  );
  return aggregationChanged || utrvConfigChanged;
};

export const getAggregationOptionsByValueType = (valueType: UtrValueType, mode: AggregationMode) => {
  const compatibleAggregations = getCompatibleAggregations(valueType, mode);

  // Always include the "default" option first
  const options: { label: string; value: ValueAggregation | 'default' }[] = [
    {
      label: aggregationLabels['default'],
      value: 'default',
    },
  ];

  // Add compatible aggregation options
  if (compatibleAggregations.length > 0) {
    const aggregationOptions = compatibleAggregations.map((option) => ({
      label: aggregationLabels[option] ?? option,
      value: option,
    }));
    options.push(...aggregationOptions);
  }

  return options;
};
