import { useContext, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>dal<PERSON>ody, ModalFooter } from 'reactstrap';
import { QuestionManagementContext } from '@features/question-management/QuestionManagementContainer';
import { MetricOverrideProps } from '../types';
import { MultipleMetricOverride } from './MultipleMetricOverride';
import { SingleMetricOverride } from './SingleMetricOverride';
import { UtrvConfigCode, UtrvConfigType, UtrvConfigValue } from './constants';
import { hasDataChanged } from './utils';
import { Loader } from '@g17eco/atoms/loader';
import { AggregationConfig } from '@g17eco/types/universalTracker';
import { BulkActionUtr } from '@g17eco/types/survey-question-list';

interface Props extends Pick<MetricOverrideProps, 'handleCloseModal'> {
  isLoading: boolean;
  initialUtrvConfig: UtrvConfigType;
  initialAggregationConfig?: AggregationConfig;
  selectedQuestions: BulkActionUtr[];
  isMultipleUpdate: boolean;
  handleUpdate: (utrvConfig: Partial<UtrvConfigType>) => void;
  handleAggregationConfigUpdate: (aggregationConfig: AggregationConfig) => void;
}

export const MetricOverrideForm = (props: Props) => {
  const {
    isLoading,
    initialUtrvConfig,
    initialAggregationConfig,
    selectedQuestions,
    isMultipleUpdate,
    handleUpdate,
    handleAggregationConfigUpdate,
    handleCloseModal,
  } = props;
  const [utrvConfig, setUtrvConfig] = useState(initialUtrvConfig);
  const [aggregationConfig, setAggregationConfig] = useState(initialAggregationConfig);

  const onChange = ({ code, value }: { code: UtrvConfigCode; value: UtrvConfigValue }) => {
    setUtrvConfig((prev) => ({ ...prev, [code]: value }));
  };

  const { utrvConfigCode } = useContext(QuestionManagementContext);
  const hasAnythingChanged = hasDataChanged({
    initialUtrvConfig,
    currentUtrvConfig: utrvConfig,
    initialAggregationConfig,
    currentAggregationConfig: aggregationConfig,
  });
  const isAllowedToUpdate = hasAnythingChanged || isMultipleUpdate;

  const onClickUpdate = () => {
    if (!isAllowedToUpdate) {
      return;
    }
    // update one specific property for multiple questions, ex: { evidenceRequired: 'optional' }
    if (utrvConfigCode) {
      const selectedUtrvConfig = { [utrvConfigCode]: utrvConfig[utrvConfigCode] };
      return handleUpdate(selectedUtrvConfig);
    }
    // update whole utrv config for single question
    // ex: { noteRequired: 'default', evidenceRequired: 'optional', verificationRequired: 'required' }
    handleUpdate(utrvConfig);

    // Also update aggregation config if they changed
    if (aggregationConfig && JSON.stringify(aggregationConfig) !== JSON.stringify(initialAggregationConfig)) {
      handleAggregationConfigUpdate(aggregationConfig);
    }
  };

  return (
    <>
      <ModalBody>
        {isLoading ? <Loader /> : null}
        {isMultipleUpdate ? (
          <MultipleMetricOverride
            hasAnythingChanged={hasAnythingChanged}
            utrvConfig={utrvConfig}
            handleChange={onChange}
          />
        ) : (
          <SingleMetricOverride
            utrvConfig={utrvConfig}
            aggregationConfig={aggregationConfig}
            selectedQuestion={selectedQuestions[0]}
            handleChange={onChange}
            handleAggregationConfigChange={setAggregationConfig}
          />
        )}
      </ModalBody>
      <ModalFooter>
        <Button color='transparent' onClick={handleCloseModal}>
          Cancel
        </Button>
        <Button color='primary' disabled={isLoading || !isAllowedToUpdate} onClick={onClickUpdate}>
          Save
        </Button>
      </ModalFooter>
    </>
  );
};
