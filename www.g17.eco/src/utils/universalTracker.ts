/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import {
  AggregationMode,
  Alternative,
  TableAggregationType,
  TableColumn,
  TableColumnType,
  UniversalTrackerBase,
  UniversalTrackerBlueprintMin,
  UniversalTrackerMin,
  UniversalTrackerPlain,
  UtrValueType,
  ValueAggregation,
  ValueAggregationChildrenCompatibility,
  ValueAggregationSiblingsCompatibility,
  VariationDataSource,
  type ValueValidation,
} from '../types/universalTracker';
import { DataPeriods } from '@utils/dataPeriods';
import { DATE, formatDate, subtractDate } from './date';
import { getDecimalNumber, isDefinedNumber } from './number';
import { Option } from '@g17eco/types/valueList';
import { ValueData, ValueDataData } from '@g17eco/types/universalTrackerValue';
import { SURVEY } from '@constants/terminology';
import { standards } from '@g17eco/core';
import UniversalTrackerClassInterface from '../model/UniversalTrackerClassInterface';

/** @deprecated replaced by enum UtrValueType */
export const UtrValueTypes = {
  calculation: 'calculation',
  number: 'number',
  percentage: 'percentage',
  sample: 'sample',
  numericValueList: 'numericValueList',
  text: 'text',
  date: 'date',
  valueList: 'valueList',
  valueListMulti: 'valueListMulti',
  textValueList: 'textValueList',
  table: 'table',
}

export const UtrValueTypeMap = {
  [UtrValueType.Text]: 'Text',
  [UtrValueType.Date]: 'Date',
  [UtrValueType.ValueList]: 'List',
  [UtrValueType.ValueListMulti]: 'Multiple-choice List',
  [UtrValueType.NumericValueList]: 'Multiple-choice List (Numerical)',
  [UtrValueType.TextValueList]: 'List',
  [UtrValueType.Table]: 'Table',
  [UtrValueType.Number]: 'Numeric',
  [UtrValueType.Percentage]: 'Percentage',
  [UtrValueType.Sample]: 'Sample or Proportion',
};

export const primitiveNumberTypes = [
  UtrValueTypes.number,
  UtrValueTypes.percentage,
  UtrValueTypes.sample
];


export function isNumeric(utr: Pick<UniversalTrackerMin, 'valueType'>) {
  switch (utr.valueType) {
    case UtrValueTypes.number:
    case UtrValueTypes.percentage:
    case UtrValueTypes.sample:
    case UtrValueTypes.numericValueList:
      return true;
    case UtrValueTypes.text:
    case UtrValueTypes.date:
    case UtrValueTypes.valueList:
    case UtrValueTypes.valueListMulti:
    default:
      return false;
  }
}

export function canAddTarget(utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueValidation'>) {
  switch (utr.valueType) {
    case UtrValueTypes.number:
    case UtrValueTypes.percentage:
    case UtrValueTypes.numericValueList:
      return true;
    case UtrValueTypes.table:
      if (utr.valueValidation?.table?.validation?.maxRows === 1 && hasNumericColumnType(utr)) {
        return true;
      }
      return false;
    case UtrValueTypes.sample:
    case UtrValueTypes.text:
    case UtrValueTypes.date:
    case UtrValueTypes.valueList:
    case UtrValueTypes.valueListMulti:
    default:
      return false;
  }
}

export function getPeriodRange(period: DataPeriods | undefined, date: string) {
  switch (period) {
    case DataPeriods.Monthly:
      return {
        period,
        from: subtractDate(date, 1, 'months'),
        to: formatDate(date, DATE.MONTH_YEAR, true),
      };
    case DataPeriods.Quarterly:
      return {
        period,
        from: subtractDate(date, 3, 'months'),
        to: formatDate(date, DATE.MONTH_YEAR, true),
      };
    case DataPeriods.Yearly:
    default:
      return {
        period: DataPeriods.Yearly,
        from: subtractDate(date, 1, 'years'),
        to: formatDate(date, DATE.MONTH_YEAR, true),
      };
  }
}

export const isCheckboxType = (type: string) => [
  UtrValueTypes.numericValueList,
  UtrValueTypes.textValueList,
].includes(type)

export function getTableConfiguration(utr: UniversalTrackerClassInterface, ignoreType = false) {
  const u = utr.getRaw() || utr;
  const checkTableType = !ignoreType && u.valueType !== 'table'
  if (checkTableType || !u.valueValidation) {
    return undefined;
  }
  return u.valueValidation.table;
}

// Handle conversion between textValueList/numericValueList to valueListMulti
const getValueDataMulti = (data: ValueData['data']) => {
  if (Array.isArray(data)) {
    return data;
  }

  if (typeof data === 'object') {
    return Object.keys(data);
  }
  return [];
};

interface ValueDataArrayProps {
  utr: UniversalTrackerClassInterface;
  data: ValueData['data'];
  unit: string;
  numberScale?: string;
  displayCheckbox?: {
    [key: string]: boolean;
  };
  value: number | string | undefined;
  options: Option[];
}

export interface ValueDataArrayReturn {
  // This is raw data, not the label...
  label?: string;
  // Actually resolved label to value list option name
  value: string | number;
  unit: string;
  numberScale?: string;
}

export function getValueDataArray(props: ValueDataArrayProps): ValueDataArrayReturn[] {
  const { utr, data, unit, numberScale, displayCheckbox, value } = props;

  // Priority to options passed in props, should deprecate utr class
  // as some API does not resolve valueList and we run into issue where labels are not available
  const valueListOptions = props.options.length ? props.options : utr.getValueListOptions();

  switch (utr.getValueType()) {
    case UtrValueTypes.valueList:
      return [{
        // @TODO: This is label not resolved here?
        label: data,
        value: data ? getValueListData(data, valueListOptions) : 'N/A',
        unit,
        numberScale,
      }];
    case UtrValueTypes.valueListMulti:
      return getValueDataMulti(data).map((k) => ({
        // @TODO: This is label not resolved here?
        label: k,
        value: getValueListData(k, valueListOptions),
        unit,
        numberScale,
      }));
    case UtrValueTypes.numericValueList:
      return valueListOptions.map((item: { code: string }) => {
        const k = item.code;
        return {
          label: getValueListData(k, valueListOptions),
          value: (!displayCheckbox || displayCheckbox[k]) && data && !isNaN(data[k]) ? data[k] : 'N/A',
          unit,
          numberScale,
        }
      });
    case UtrValueTypes.textValueList:
      return valueListOptions.map((item: { code: string }) => {
        const k = item.code;
        return {
          label: getValueListData(k, valueListOptions),
          value: (!displayCheckbox || displayCheckbox[k]) && data && data[k] ? data[k] : 'N/A',
          unit,
          numberScale,
        }
      });
    default:
      return [{
        value: convertToNumberOrString(value),
        unit,
        numberScale,
      }];
  }
}

const convertToNumberOrString = (value: undefined | string | number): number | string => {
  if (value === undefined || isNaN(value as any)) {
    return 'N/A';
  }
  return value;
}

export const getQuestionShortPrefix = (utr: UniversalTrackerClassInterface) => {
  const alternativeCode = utr.getType();
  return utr.getShortPrefix(alternativeCode);
}

export const getQuestionName = (utr: UniversalTrackerClassInterface) => {
  const alternativeCode = utr.getType();
  return utr.getName(alternativeCode);
}

function __hasAlternativeInfo(utr: UniversalTrackerPlain, alternativeCode = '') {
  if (!utr.alternatives) {
    return false;
  }
  return alternativeCode in utr.alternatives;
}

function __get(attribute: keyof UniversalTrackerPlain | keyof Alternative, utr: UniversalTrackerPlain, alternativeCodes?: string[]): unknown {
  let returnAttribute = utr[attribute as keyof UniversalTrackerPlain];

  if (!alternativeCodes) {
    return returnAttribute;
  }

  alternativeCodes.forEach(code => {
    if (code === utr.type) {
      return returnAttribute = utr[attribute as keyof UniversalTrackerPlain];
    }
    if (__hasAlternativeInfo(utr, code)) {
      return returnAttribute = utr.alternatives?.[code]?.[attribute as keyof Alternative];
    }
  });

  return returnAttribute;
}

export function getName(utr: UniversalTrackerPlain, alternativeCodes?: string[]): string { return __get('name', utr, alternativeCodes) as string; }

export function getTypeCode(utr: UniversalTrackerPlain, alternativeCodes?: string[]): string { return __get('typeCode', utr, alternativeCodes) as string; }

export const isNumericTableColumnType = (type: TableColumnType) => {
  return [TableColumnType.Number, TableColumnType.Percentage].includes(type);
};

export const isNumericValueType = (valueType: UtrValueType) => {
  return [UtrValueType.Number, UtrValueType.Percentage, UtrValueType.NumericValueList].includes(valueType);
};

export const hasNumericColumnType = ({ valueValidation }: Pick<UniversalTrackerPlain, 'valueValidation'>) => {
  const columns = valueValidation?.table?.columns;
  return columns?.some((column) => isNumericTableColumnType(column.type));
};

export const isSimpleNumericColumnType = (column: Pick<TableColumn, 'type' | 'calculation'>) => {
  return [TableColumnType.Number, TableColumnType.Percentage].includes(column.type) && !column.calculation?.formula;
};

export const hasSimpleNumericColumnType = ({
  valueValidation,
}: Pick<UniversalTrackerPlain, 'valueValidation'>) => {
  const columns = valueValidation?.table?.columns;
  return columns?.some((column) => isSimpleNumericColumnType(column));
};

export const hasNumericInput = (utr: Pick<UniversalTrackerClassInterface, 'getValueType' | 'getValueValidation'>) =>
  isNumericValueType(utr.getValueType()) || hasSimpleNumericColumnType({ valueValidation: utr.getValueValidation() });

export const isNumberOrTextValueListType = (utr: UniversalTrackerClassInterface) => {
  return [UtrValueType.NumericValueList, UtrValueType.TextValueList].includes(utr.getValueType());
}

export const isSingleRowTableType = (utr: UniversalTrackerClassInterface | Pick<UniversalTrackerPlain, 'valueType' | 'valueValidation'>) => {
  if ('getValueType' in utr) {
    // UTR must be UniversalTracker
    const tableConfiguration = getTableConfiguration(utr);
    return utr.getValueType() === UtrValueType.Table && tableConfiguration?.validation?.maxRows === 1;
  }
  return utr.valueType === UtrValueType.Table && Number(utr.valueValidation?.table?.validation?.maxRows) === 1;
};

export const isMultiRowTableType = (utr: UniversalTrackerClassInterface | Pick<UniversalTrackerPlain, 'valueType' | 'valueValidation'>) => {
  if ('getValueType' in utr) {
    // UTR must be UniversalTracker
    const tableConfiguration = getTableConfiguration(utr);
    return utr.getValueType() === UtrValueType.Table && tableConfiguration?.validation?.maxRows !== 1;
  }
  return utr.valueType === UtrValueType.Table && Number(utr.valueValidation?.table?.validation?.maxRows) !== 1;
};

export const hasStandard = (utr: Pick<UniversalTrackerBlueprintMin, 'type' | 'alternatives'>, code: string) => {
  return utr.type === code || !!utr.alternatives?.[code];
};

export const hasFramework = (utr: Pick<UniversalTrackerBlueprintMin, 'tags'>, subTag: string) => {
  if (!utr.tags) {
    return false;
  }

  return Object.values(utr.tags).flat().includes(subTag);
};

export const getUtrNumberValue = ({
  value,
  decimal,
  hasValueChanged,
  fallback = '',
}: {
  value: string | number | undefined;
  decimal: number | undefined;
  hasValueChanged: boolean | undefined;
  fallback?: number | string;
}) => {
  if (!isDefinedNumber(value)) {
    return fallback;
  }

  if (!isDefinedNumber(decimal) || hasValueChanged) {
    return value;
  }

  // On first render, the value type must be number as it is taken from API
  // Only then should do automatic decimal addition, not on value change
  return typeof value === 'number' ? getDecimalNumber(value, decimal) : value;
};


const getValueListOptionByCode = (options: Option[], code: string) => {
  const option = options.find((option) => option.code === code);
  if (option === undefined) {
    return code;
  }
  return option.name;
}

export const getValueListData = (data: ValueDataData | undefined, options: Option[]) => {
  if (typeof data === 'string') {
    return getValueListOptionByCode(options, data);
  }
  return '';
}

export const getValueListMultiData = (data: ValueDataData | undefined, options: Option[]) => {
  if (Array.isArray(data)) {
    return data.map((code) => getValueListOptionByCode(options, code));
  }
  return [];
}

export const getUtrDecimal = (
  utr: Pick<UniversalTrackerClassInterface, 'getValueType' | 'getValueValidation'> | UniversalTrackerBase | undefined,
  inputName?: string
) => {
  if (!utr) {
    return;
  }
  const valueType: UtrValueType = 'getValueType' in utr ? utr.getValueType() : (utr.valueType as UtrValueType);

  const valueValidation: ValueValidation | undefined =
    'getValueValidation' in utr ? utr.getValueValidation() : utr.valueValidation;

  if (!valueValidation) {
    return;
  }

  if (isNumericValueType(valueType)) {
    return valueValidation?.decimal;
  }

  if (hasSimpleNumericColumnType({ valueValidation })) {
    return getTableColumnDecimal(valueValidation, inputName);
  }

  return;
};

const getTableColumnDecimal = (valueValidation: ValueValidation, inputName?: string) => {
  const tableColumn = valueValidation.table?.columns.find((col: TableColumn) => col.code === inputName);

  if (!tableColumn || !isSimpleNumericColumnType(tableColumn) || tableColumn.calculation) {
    return;
  }

  return tableColumn?.validation?.decimal;
};

export const getInstructionLabel = (alternativeCode: string = '') => {
  return alternativeCode === 'gri'
    ? 'See complete Reporting Guidance and Reporting Recommendation here'
    : `View ${standards[alternativeCode]?.shortName || ''} Instructions`;
};

export const isTableGroupAggregation = (utr: Pick<UniversalTrackerPlain, 'valueValidation'>) => {
  return utr.valueValidation?.table?.aggregation?.type === TableAggregationType.Group;
}

export const VARIATION_DATA_SOURCE_LABEL_MAP = {
  [VariationDataSource.LastMonth]: `Last month’s ${SURVEY.SINGULAR}`,
  [VariationDataSource.LastYear]: `Last year’s ${SURVEY.SINGULAR}`,
  [VariationDataSource.LatestVerified]: 'Latest verified answer',
};

export const aggregationLabels: { [key in ValueAggregation | 'default']: string } = {
  default: 'Use default settings (what they are)',
  [ValueAggregation.LatestAggregator]: 'LATEST VALUE: Most recently submitted value',
  [ValueAggregation.ValueSumAggregator]: 'SUM: Add up all values',
  [ValueAggregation.ValueAverageAggregator]: 'AVERAGE: Simple average',
  [ValueAggregation.ValueCountAggregator]: 'COUNT: Count of submitted instance for this input',
  [ValueAggregation.EmptyAggregator]: 'IGNORE: Do not aggregate',
  [ValueAggregation.ValueConcatenateAggregator]: 'CONCAT: Concat submitted values',
  [ValueAggregation.ValueListCountAggregator]:
    'TEXT VALUE LIST COUNT: Create a list of possible options with their respective counts',
  [ValueAggregation.TextCountAggregator]:
    'TEXT LIST COUNT: Create a list of possible text options with their respective counts',
  [ValueAggregation.NumericValueListSumAggregator]: 'VALUE LIST SUM: Add up each list option',
  [ValueAggregation.NumericValueListAverageAggregator]: 'VALUE LIST AVERAGE: Simple average from each list option',
  [ValueAggregation.TableColumnAggregator]:
    'TABLE: Aggregation will respect the aggregation method specified in the configuration for the column',
  [ValueAggregation.TableRowGroupAggregator]:
    'TABLE ROW GROUP: Aggregation will group by columns, and then respect the aggregation method specified in the configuration for the column',
  [ValueAggregation.TableConcatenationAggregator]: 'TABLE CONCATENATION: Appends all rows to table',
};

export const getCompatibleAggregations = (valueType: UtrValueType, mode: AggregationMode): ValueAggregation[] => {
  const compatibility =
    mode === AggregationMode.Combined ? ValueAggregationSiblingsCompatibility : ValueAggregationChildrenCompatibility;

  const typeConfig = compatibility[valueType];
  return typeConfig ? typeConfig.compatible : [];
};