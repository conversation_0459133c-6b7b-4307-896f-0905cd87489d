import { UtrvConfigType } from '../features/question-configuration/types';
import { UniversalTrackerPlain , AggregationConfig } from './universalTracker';

export interface InitiativeUniversalTracker
  extends Pick<UniversalTrackerPlain, 'unitInput' | 'numberScaleInput' | 'valueValidation' | 'unitLocked' | 'numberScaleLocked'> {
  _id: string;
  initiativeId: string;
  universalTrackerId: string;
  utrvConfig?: UtrvConfigType;
  aggregationConfig?: AggregationConfig;
}
