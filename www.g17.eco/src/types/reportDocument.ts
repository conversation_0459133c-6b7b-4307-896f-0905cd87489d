/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

export enum ReportDocumentTemplate {
  /** Empty state **/
  Blank = 'blank',
  /** Allow AI to Generate the initial template state **/
  AiGenerated = 'ai_generated',
  /** Simple template with a few pre-defined sections */
  Simple = 'simple',
}

export interface ReportDocumentConfig {
  template: ReportDocumentTemplate;
}

export enum ReportDocumentStatus {
  /**
   * Report document is created, but lexical state not yet generated
   */
  Pending = 'pending',
  /**
   * Lexical state generated, but not yet synced to web socket server
   */
  Generated = 'generated',
  /**
   * Lexical state synced to web socket server, ready for editing
   */
  Completed = 'completed',
  /**
   * Error occurred during lexical state generation
   */
  Error = 'error',
}

export interface ReportDocument {
  _id: string;
  title: string;
  description?: string;

  type: string;

  initiativeId: string;
  createdBy: string

  /** ISO string */
  lastUpdated: string;

  /** ISO string */
  created: string;

  /** @todo: Currently optional for existing data, should do migration to remove optional */
  config?: ReportDocumentConfig;

  status: ReportDocumentStatus;

  /** Optional survey ID to generate report from */
  surveyId?: string;
}

export type CreateReportDocumentMin = Pick<ReportDocument,
  | 'type'
  | 'title'
  | 'description'
  | 'initiativeId'
  | 'config'
  | 'surveyId'
> & { _id?: string };
