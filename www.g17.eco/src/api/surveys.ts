import type { <PERSON><PERSON>in<PERSON>lain } from '@g17eco/types/initiative';
import type { ApiResponse } from '../types/api';
import type { SurveyInitiative, SurveyListItem } from '@g17eco/types/survey';
import type { SurveyActionData } from '@g17eco/types/surveyAction';
import type { MetricActionType } from '@features/survey/overview/constants';
import { g17ecoApi, transformResponse } from './g17ecoApi';

interface SurveyScopeUpdateMutationParams {
  action: MetricActionType;
  surveyId: string;
}

export type SurveyById = Pick<SurveyInitiative, '_id' | 'name' | 'effectiveDate' | 'initiativeId'> & {
  initiative: Pick<InitiativeMinPlain, '_id' | 'name'>;
};

export const SURVEY_DATA_TAG = 'survey';
const UTR_SURVEYS_TAG = 'utr-surveys';
const INITIATIVE_SURVEYS_TAG = 'initiative-surveys';

export const surveyScopeUpdateApi = g17ecoApi
  .enhanceEndpoints({
    addTagTypes: ['surveyScopeUpdate', SURVEY_DATA_TAG, UTR_SURVEYS_TAG, INITIATIVE_SURVEYS_TAG],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      handleScopeUpdate: builder.mutation<ApiResponse, SurveyScopeUpdateMutationParams>({
        invalidatesTags: ['surveyScopeUpdate'],
        query: ({ action, surveyId }) => ({
          url: `/surveys/${surveyId}/blueprint/${action}`,
          method: 'patch',
        }),
      }),
      getSurveyById: builder.query<SurveyActionData, string>({
        transformResponse,
        query: (surveyId) => ({
          url: `/surveys/${surveyId}`,
          method: 'get',
        }),
        providesTags: [SURVEY_DATA_TAG],
      }),
      getSurveysByUtrId: builder.query<SurveyById[], string>({
        transformResponse,
        query: (utrId) => ({
          url: `/surveys/utr/${utrId}`,
          method: 'get',
        }),
        providesTags: [UTR_SURVEYS_TAG],
      }),
      getSurveysForInitiative: builder.query<SurveyListItem[], string>({
        transformResponse,
        query: (initiativeId) => ({
          url: `/initiatives/${initiativeId}/surveys`,
          method: 'get',
        }),
        providesTags: (_result, _error, initiativeId) => [{ type: INITIATIVE_SURVEYS_TAG, id: initiativeId }],
      }),
    }),
  });

export const {
  useHandleScopeUpdateMutation,
  useGetSurveyByIdQuery,
  useGetSurveysByUtrIdQuery,
  useGetSurveysForInitiativeQuery,
} = surveyScopeUpdateApi;
